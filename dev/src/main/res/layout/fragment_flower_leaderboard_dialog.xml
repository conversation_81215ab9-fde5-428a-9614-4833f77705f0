<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/flower_leaderboard_dialog_bg">

    <!-- 主标题和问号按钮 -->
    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_32"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S18.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/flower_leaderboard_title"
            android:textColor="@color/colorPrimaryDark" />

        <ImageView
            android:id="@+id/iv_question"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_8"
            android:src="@drawable/icon_question" />
    </LinearLayout>

    <!-- 副标题 -->
    <LinearLayout
        android:id="@+id/layout_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_title">

        <ImageView
            android:id="@+id/iv_line_left"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_1"
            android:layout_marginEnd="@dimen/dp_8"
            android:src="@drawable/flower_leaderboard_dialog_line_left" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_subtitle"
            style="@style/MetaTextView.S12.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/flower_leaderboard_subtitle"
            android:textColor="@color/color_9242FF" />

        <ImageView
            android:id="@+id/iv_line_right"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_1"
            android:layout_marginStart="@dimen/dp_8"
            android:src="@drawable/flower_leaderboard_dialog_line_right" />
    </LinearLayout>

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 排行榜列表 -->
    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rv_leaderboard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_16"
        app:layout_constraintBottom_toTopOf="@id/layout_current_user"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_subtitle"
        tools:itemCount="8"
        tools:listitem="@layout/item_flower_leaderboard" />

    <!-- 当前用户信息 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_current_user"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_58"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_flower_current_user_other"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_8"
        app:layout_constraintBottom_toTopOf="@id/btn_send_flower"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_current_user_avatar"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_default_avatar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/circleStyle" />

        <TextView
            android:id="@+id/tv_current_user_name"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_22"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@color/colorPrimaryDark"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/layout_current_user_rank"
            app:layout_constraintStart_toEndOf="@id/iv_current_user_avatar"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="User Name" />

        <LinearLayout
            android:id="@+id/layout_current_user_rank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/dp_4">

                <TextView
                    android:id="@+id/tv_current_user_rank"
                    style="@style/MetaTextView.S12.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_9242FF"
                    tools:text="NO.10" />

                <ImageView
                    android:id="@+id/iv_current_user_rank"
                    android:layout_width="@dimen/dp_29"
                    android:layout_height="@dimen/dp_20"
                    android:visibility="gone"
                    tools:src="@drawable/flower_leaderboard_self_no1"
                    tools:visibility="visible" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/layout_current_user_flower"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:background="@drawable/bg_flower_count"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp_6"
                android:paddingVertical="@dimen/dp_2">

                <ImageView
                    android:layout_width="@dimen/dp_13"
                    android:layout_height="@dimen/dp_13"
                    android:src="@drawable/ic_single_flower" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_current_user_flower_count"
                    style="@style/MetaTextView.S10.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:textColor="@color/color_9242FF"
                    tools:text="×99" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 送花按钮 -->
    <com.socialplay.gpark.ui.view.IconTextView
        android:id="@+id/btn_send_flower"
        style="@style/Button.S14.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_36"
        android:layout_marginBottom="@dimen/dp_55"
        android:background="@drawable/bg_flower_send_button"
        android:gravity="center"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:text="@string/flower_leaderboard_send_flower"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:iconHeight="@dimen/dp_24"
        app:iconPadding="@dimen/dp_8"
        app:iconPosition="left"
        app:iconSrc="@drawable/ic_single_flower"
        app:iconWidth="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 空数据布局 -->
    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_30"
        android:visibility="visible"
        app:showBtn="true"
        app:btnText="Go give feedback"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_subtitle"
        tools:btnText="Go give feedback"/>

<!--    &lt;!&ndash; 反馈按钮 &ndash;&gt;-->
<!--    <com.socialplay.gpark.ui.view.IconTextView-->
<!--        android:id="@+id/btn_go_feedback"-->
<!--        style="@style/Button.S14.PoppinsMedium500"-->
<!--        android:layout_width="@dimen/dp_180"-->
<!--        android:layout_height="@dimen/dp_48"-->
<!--        android:layout_marginTop="@dimen/dp_24"-->
<!--        android:layout_marginBottom="@dimen/dp_134"-->
<!--        android:background="@drawable/bg_ffdc1c_round_40"-->
<!--        android:gravity="center"-->
<!--        android:paddingStart="@dimen/dp_16"-->
<!--        android:paddingEnd="@dimen/dp_16"-->
<!--        android:text="@string/go_give_feedback"-->
<!--        android:textAllCaps="false"-->
<!--        android:textColor="@color/color_1A1A1A"-->
<!--        android:visibility="gone"-->
<!--        app:iconHeight="@dimen/dp_16"-->
<!--        app:iconPadding="@dimen/dp_8"-->
<!--        app:iconPaddingBottom="@dimen/dp_0"-->
<!--        app:iconPaddingLeft="@dimen/dp_0"-->
<!--        app:iconPaddingRight="@dimen/dp_0"-->
<!--        app:iconPaddingTop="@dimen/dp_0"-->
<!--        app:iconPosition="left"-->
<!--        app:iconSrc="@drawable/icon_flower_go_feedback"-->
<!--        app:iconWidth="@dimen/dp_16"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/loading" />-->

</androidx.constraintlayout.widget.ConstraintLayout>

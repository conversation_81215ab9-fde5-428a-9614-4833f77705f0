package com.socialplay.gpark.ui.outfit

import android.content.ComponentCallbacks
import android.content.Context
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.asset.AssetEditResultEvent
import com.socialplay.gpark.data.model.guide.UgcModuleGuidInfo
import com.socialplay.gpark.data.model.outfit.UgcAssetEntrance
import com.socialplay.gpark.data.model.outfit.UgcAssetProfileEntrance
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTag
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.default
import com.socialplay.gpark.ui.core.maverick.ifLoading
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.maverick.mapRetained
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenWidth
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc: 已发布ugc
 */
data class ProfileUgcDesignState(
    val uuid: String,
    val isMe: Boolean,
    val size: Int,
    val tags: List<UgcDesignProfileTag>,
    val tagCode: Int = tags.first().code,
    val tabPosition: Int = 0,
    val entrances: Async<List<UgcAssetProfileEntrance>> = Uninitialized,
    val showGuideBtn: Async<Boolean> = Uninitialized,
    val template: Async<EditorTemplate> = Uninitialized,
) : MavericksState

class ProfileUgcDesignViewModel(
    initialState: ProfileUgcDesignState,
    private val ugcRepo: UgcRepository,
    val accountInteractor: AccountInteractor,
    private val tTaiInteractor: TTaiInteractor,
    private val repo: IMetaRepository,
) : BaseViewModel<ProfileUgcDesignState>(initialState) {

    val showGuideBtn
        get() = PandoraToggle.enableModuleGuideProfileAssetTabEmpty
                && oldState.showGuideBtn() == true
                && accountInteractor.moduleGuideStatus <= BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO

    init {
        load()
    }

    fun updateSize(size: Int) = withState { s ->
        if (size != s.size) {
            setState { copy(size = size) }
            load(true)
        }
    }

    fun updateUuid(newUuid: String) = withState { s ->
        if (s.uuid == newUuid) return@withState
        setState { copy(uuid = newUuid) }
        load(force = true)
    }

    fun load(force: Boolean = false) = withState { s ->
        if ((!force && s.entrances is Loading) || s.uuid.isEmpty() || accountInteractor.curUuid.isEmpty()) return@withState
        ugcRepo.getUgcAssetProfile(s.uuid, s.size, s.tagCode).asFlow().map {
            it.filter { it.isSupported && !it.items.isNullOrEmpty() }
                .map { it.copy(items = it.items?.distinctBy { it.itemId }) }
        }.flatMapConcat { list ->
            if (PandoraToggle.enableModuleGuideProfileAssetTabEmpty
                && s.isMe
                && list.isEmpty()
            ) {
                if (accountInteractor.moduleGuideStatus < BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO) {
                    ugcRepo.getModuleGuideStatus().map {
                        it to list
                    }
                } else {
                    flow { emit(DataResult.Success(accountInteractor.moduleGuideStatus) to list) }
                }
            } else {
                // 视为已完成
                flow { emit(DataResult.Success(BaseAccountInteractor.MODULE_GUIDE_STATUS_API_DONE) to list) }
            }
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (guide, list) = result()
                    copy(
                        entrances = result.map { list },
                        showGuideBtn = if (guide.succeeded) {
                            result.map { guide.data == BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO || guide.data == BaseAccountInteractor.MODULE_GUIDE_STATUS_SKIP_USER }
                        } else {
                            showGuideBtn
                        }
                    )
                }

                else -> {
                    copy(entrances = result.mapRetained(entrances).default(entrances.invoke()))
                }
            }
        }
    }

    fun getModuleTemplate() = withState { s ->
        if (s.template is Loading) return@withState
        tTaiInteractor.getTTaiWithTypeV3<UgcModuleGuidInfo>(TTaiKV.ID_MODULE_GUIDE_ID)
            .flatMapConcat {
                val templateId = it?.templateIds?.random()
                check(!templateId.isNullOrBlank())
                repo.getGameTemplateV2(templateId, 10).asFlow()
            }.map {
                it.type = EditorConfigJsonEntity.TYPE_MODULE
                it
            }.execute(retainValue = ProfileUgcDesignState::template) {
                copy(template = it)
            }
    }

    fun updateTabSelection(position: Int, code: Int, callback: () -> Unit) = withState { s ->
        if (s.entrances is Loading) return@withState
        if (s.tabPosition != position || s.tagCode != code) {
            when (code) {
                UgcAssetEntrance.TYPE_DESIGN -> {
                    Analytics.track(
                        EventConstants.PROFILE_HOME_MYWORKS_TAB_CLICK
                    )
                }

                UgcAssetEntrance.TYPE_GOODS -> {
                    Analytics.track(
                        EventConstants.PROFILE_HOME_SHOP_TAB_CLICK
                    )
                }
            }
            setState {
                copy(tabPosition = position, tagCode = code)
            }
            load(true)
            callback()
        }
    }

    fun updateItem(result: AssetEditResultEvent?) = withState { s ->
        result ?: return@withState
        val oldListResult = s.entrances
        val oldList = oldListResult() ?: return@withState
        var changed = false
        val newList = if (result.deleted == true) {
            buildList<UgcAssetProfileEntrance> {
                oldList.forEach {
                    val prevSize = it.items?.size ?: 0
                    val newItems = it.items?.filter { it.itemId != result.feedId }
                    val newSize = newItems?.size ?: 0
                    if (prevSize != newSize) {
                        if (newSize > 0) {
                            add(it.copy(items = newItems))
                        }
                        changed = true
                    } else {
                        add(it)
                    }
                }
            }
        } else {
            return@withState
        }
        if (!changed) return@withState
        setState {
            copy(entrances = entrances.copyEx(newList))
        }
    }

    companion object :
        KoinViewModelFactory<ProfileUgcDesignViewModel, ProfileUgcDesignState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ProfileUgcDesignState
        ): ProfileUgcDesignViewModel {
            return ProfileUgcDesignViewModel(state, get(), get(), get(), get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): ProfileUgcDesignState {
            val args = viewModelContext.args as ProfileUgcDesignFragmentArgs
            val context: Context = get()
            val size = ((context.screenWidth - context.dp(24)) / context.dp(88)).coerceAtLeast(4)

            return ProfileUgcDesignState(
                args.uuid,
                args.isMe,
                size,
                listOf(
                    UgcDesignProfileTag(
                        UgcAssetEntrance.TYPE_DESIGN,
                        viewModelContext.activity.getString(R.string.profile_asset_type_works)
                    ),
                    UgcDesignProfileTag(
                        UgcAssetEntrance.TYPE_GOODS,
                        viewModelContext.activity.getString(R.string.profile_asset_type_goods)
                    )
                ),
                showGuideBtn = if (!args.isMe || !PandoraToggle.enableModuleGuideProfileAssetTabEmpty) {
                    Success(false)
                } else {
                    Uninitialized
                }
            )
        }
    }
}
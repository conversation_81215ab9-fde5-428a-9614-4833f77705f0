package com.socialplay.gpark.ui.im.conversation

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.LayoutPrivateChatTitleBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.im.FriendStatusHelper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

class PrivateChatFragment : BaseChatFragment() {
    private val privateChatViewModel: PrivateChatViewModel by fragmentViewModel()
    private lateinit var titleBinding: LayoutPrivateChatTitleBinding
    private val args by navArgs<PrivateChatFragmentArgs>()
    override val destId: Int = R.id.private_chat_fragment

    //好友名称
    var title: String? = ""
    override val conversationType: Conversation.ConversationType =
        Conversation.ConversationType.PRIVATE

    override fun getPageName(): String = PageNameConstants.CONVERSATION_PRIVATE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.run {
            val bundle = PrivateChatFragmentArgs.fromBundle(this)
            targetId = bundle.otherUid
            title = bundle.title
            Timber.d("Private chat users id %s", targetId)
        }
    }

    override fun isCurrentConversionMessage(msg: Message): Boolean {
        return targetId == msg.targetId
                && conversationType == msg.conversationType
                && !(msg.messageId.equals("0") || msg.messageId.equals("-1"))
    }

    override fun initTitleView() {
        titleBinding = LayoutPrivateChatTitleBinding.inflate(layoutInflater)
        binding.titleBarLayout.addView(titleBinding.root)

        binding.titleBarLayout.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_CONVERSATION_HOME_PAGE_CLICK)
        }

        titleBinding.ivTitleBack.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_CONVERSATION_BACK_CLICK)
            navigateUp()
        }

        titleBinding.ivTitleSetting.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_CONVERSATION_SETTING_CLICK)
            toSetting()
        }

        titleBinding.llBarInfo.setOnAntiViolenceClickListener {
            MetaRouter.IM.goAddFriendDialog(this, targetId!!)
        }

        titleBinding.llBarInfo.setOnAntiViolenceClickListener {
            MetaRouter.IM.goAddFriendDialog(this, targetId!!)
        }
    }

    override fun initPageOnceData() {
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
        }
        title?.let { updateTitle(it, args.userLabelInfo?.tagIds, args.userLabelInfo?.labelInfo) }
        targetId?.let { chatViewModel.quickSharing(args.shareContent, it) }
        //获取用户的备注
        targetId?.let {
            privateChatViewModel.addMessageListener(it)
            privateChatViewModel.getFriendInfo(it, title)
            chatViewModel.checkBlockRelation(it)
        }
        //监听输入状态
        privateChatViewModel.getTypingStatus()
        //好友状态监听
        imInteractor.IMConnectionStatus.observe(viewLifecycleOwner) {
            Timber.i("rongConnectionStatus:" + it)
            val isShow = !it || !NetUtil.isNetworkAvailable()
            binding.vConnectionStatusLayout.visible(isShow)
        }
    }

    override fun initPageObserver() {
        privateChatViewModel.friendInfoLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                val name = if (!it.remark.isNullOrEmpty()) it.remark!! else it.name ?: ""
                updateTitle(name, it.tagIds, it.labelInfo)
                if (it.isFriend()) {
                    titleBinding.ivOnline.visible()
                    titleBinding.tvFriendStatus.visible()
                    FriendStatusHelper.updateStatus(
                        it.uuid,
                        it.status,
                        titleBinding.tvFriendStatus,
                        FriendStatusHelper.SOURCE_CHAT,
                        titleBinding.ivOnline
                    )
                } else {
                    titleBinding.ivOnline.gone()
                    titleBinding.tvFriendStatus.gone()
                }
            } else {
                titleBinding.ivOnline.gone()
                titleBinding.tvFriendStatus.gone()
            }
        }
        privateChatViewModel.typingStatusLiveData.observe(viewLifecycleOwner) {
            updateTypingStatus(it)
        }
    }

    override fun initData() {
        targetId?.let { targetId ->
            chatViewModel.getUnreadCount(targetId, Conversation.ConversationType.PRIVATE)
        }
    }

    override fun onSendBtnClicked(text: String?) {
        Analytics.track(
            EventConstants.EVENT_CONVERSATION_TEXT_SEND_CLICK,
            "type" to "1",
            "message_type" to "1"
        )
        if (text != null && !TextUtils.isEmpty(text) && !TextUtils.isEmpty(text.trim { it <= ' ' })) {
            targetId?.let { targetId ->
                if (privateChatViewModel.checkFriendStatus(targetId)) {
                    chatViewModel.sendTextMessageWithRiskReview(
                        targetId,
                        conversationType,
                        text,
                    )
                } else {
                    privateChatViewModel.accountInteractor.accountLiveData.value?.uuid?.let { uuid ->
                        chatViewModel.sendSystem(
                            targetId = targetId,
                            uuid,
                            conversationType,
                            getString(R.string.friend_bot_both)
                        )
                    }
                }
            }
        } else {
            Timber.e("PrivateFragment text content must not be null")
        }
    }

    override fun getRole(userId: String): CustomMessageListAdapter.Role {
        return CustomMessageListAdapter.Role.PRIVATE_CHAT
    }

    override fun getNickname(userId: String): String? {
        return if (targetId == userId) {
            title
        } else {
            privateChatViewModel.currentUserInfo()?.nickname
        }
    }

    /**
     * 设置界面
     */
    private fun toSetting() {
        if (targetId.isNullOrBlank()) {
            toast(R.string.friend_no_user)
            return
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        MetaRouter.IM.goChatSetting(this, targetId!!) { isClearMsg, isDeleteFriend, remark ->
            Timber.d(
                "toSetting isClearMsg %s isDeleteFriend %s remark %s ",
                isClearMsg,
                isDeleteFriend,
                remark
            )
            if (isDeleteFriend) { // 执行了删除好友操作
                findNavController().popBackStack()
                return@goChatSetting
            }
            if (isClearMsg) { // 执行了清空消息操作
                chatViewModel.cleanAllMessage()
            }
            if (TextUtils.isEmpty(remark) || remark == title) {
                return@goChatSetting
            }
        }
    }

    /**
     * 更新用户昵称
     */
    private fun updateTitle(remark: String, tagIds: List<Int>?, labelInfo: LabelInfo?) {
        Timber.d("updateTitle isClearMsg updateTitle %s ", remark)
        title = remark
        titleBinding.tvChatName.text = title
        if (labelInfo != null) {
            titleBinding.labelGroup.show(tagIds, labelInfo)
        } else {
            titleBinding.labelGroup.hide()
        }
    }


    /**
     * 输入状态监听处理
     */
    private fun updateTypingStatus(it: Message.MessageType?) {
        if (it == null) {
            titleBinding.tvChatName.text = title
        } else {
            if (it == Message.MessageType.TXT) {
                titleBinding.tvChatName.text =
                    getString(R.string.seal_conversation_remote_side_is_typing)
            } else {
                titleBinding.tvChatName.text = title
            }
        }
    }

    override fun onDestroy() {
        if (args.shareContent?.isNotEmpty() == true) {
            setFragmentResult(
                SHARE_CALLBACK, bundleOf(SHARE_CALLBACK to true)
            )
        }
        super.onDestroy()
    }
}
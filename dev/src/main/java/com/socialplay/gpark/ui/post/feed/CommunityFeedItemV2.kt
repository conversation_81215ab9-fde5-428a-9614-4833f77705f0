package com.socialplay.gpark.ui.post.feed

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.GradientDrawable.Orientation
import android.graphics.drawable.LayerDrawable
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.CarouselModel_
import com.airbnb.epoxy.OnModelBoundListener
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.community.CommentInfo
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.community.Topic
import com.socialplay.gpark.data.model.community.TopicFeedCard
import com.socialplay.gpark.data.model.community.UserFeedCard
import com.socialplay.gpark.data.model.community.UserFeedUser
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.post.event.CommentMetaData
import com.socialplay.gpark.data.model.post.event.PostMetaData
import com.socialplay.gpark.data.model.video.PlayerVideoResource
import com.socialplay.gpark.databinding.AdapterRecommendCreatorBinding
import com.socialplay.gpark.databinding.AdapterRecommendCreatorMoreBinding
import com.socialplay.gpark.databinding.ItemCommunityAssetsCardBinding
import com.socialplay.gpark.databinding.ItemCommunityFeed3ImagesBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedFootV2Binding
import com.socialplay.gpark.databinding.ItemCommunityFeedHeadBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedHotCommentBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedSingleImageBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedSuggestedUserTitleBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedTopicsSquareItemBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedTopicsSquareTitleBinding
import com.socialplay.gpark.databinding.ItemCommunityFeedVideoBinding
import com.socialplay.gpark.databinding.ItemCommunityFollowedWorkBinding
import com.socialplay.gpark.databinding.ItemCommunityFollowingUserBinding
import com.socialplay.gpark.databinding.ItemCommunityGridImagesBinding
import com.socialplay.gpark.databinding.ItemCommunitySublistTitleBinding
import com.socialplay.gpark.databinding.LayoutCommunityGridImageBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.community.FeedImageVideoUtil
import com.socialplay.gpark.function.post.CommunityUtil
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.CarouselNoSnapWrapModel
import com.socialplay.gpark.ui.core.views.EpoxyCarouselBuilder
import com.socialplay.gpark.ui.core.views.EpoxyCarouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.carouselBuilder
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.divider
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.post.PostHelper
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment.Companion.POST_TAG
import com.socialplay.gpark.ui.post.feed.tag.CommunityTagSpannable
import com.socialplay.gpark.ui.view.ExpandableTextView.STATE_SHRINK
import com.socialplay.gpark.ui.view.FollowView
import com.socialplay.gpark.ui.view.video.VideoPageListView
import com.socialplay.gpark.util.DateUtilWrapper
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.getStringByGlobal
import com.socialplay.gpark.util.glide.MaxSizeCrop
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.isHttp
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class CommunityFeedVisibleInfoV2(
    val itemsVisible: MutableMap<String, Boolean> = mutableMapOf(),
) {
    fun visible(): Boolean {
        return itemsVisible.values.any { visible -> visible }
    }
}

interface ICommunityTopicListenerV2 {
    fun goTopicDetail(topic: Topic)
    fun goTopicSquare()
    fun trackTopicRankShow(topic: Topic, rank: Int)
}

interface ICommunityRecommendUsersCard {
    fun getGlideOrNull(): RequestManager?
    fun clickAvatar(user: UserFeedUser)
    fun changeFollow(user: UserFeedUser, followFrom: CommunityFollowFrom)
    fun clickMore()
    fun onUserCardItemVisible(user: UserFeedUser, type: CommunityFollowFrom)
}

enum class CommunityFollowFrom(val trackValue: String){
    FEED(EventParamConstants.LOCATION_FOLLOW_POST_FEED),
    RECOMMEND_USER_CARD(EventParamConstants.LOCATION_FOLLOW_RECOMMEND),
    CLOUD_SUGGESTED_USER(EventParamConstants.LOCATION_FOLLOW_FOLLOW_COLD),
    FOLLOWED_USER_CARD(EventParamConstants.LOCATION_FOLLOW_FOLLOW_FEED),
}

interface ICommunityFeedListenerV2 : IBaseEpoxyItemListener {
    fun calculateVideoPlay(
        resId: String,
        url: String,
        position: Int,
        videoView: VideoPageListView,
        percentVisibleHeight: Float
    )

    fun onItemVisibilityChange(item: PostFeedCard, itemUniqueKey: String, visible: Boolean)

    fun goPost(item: PostFeedCard, commentInfo: CommentInfo? = null)
    fun goGameDetail(item: PostFeedCard, gameCard: PostCardInfo)
    fun goProfile(uuid: String, from: String)
    fun openImagePreview(url: List<String>, imageViews: List<ImageView>, position: Int) {}
    fun showImageDetail(url: List<String>, position: Int) {}
    fun showVideoDetail(postId: String, url: String)
    fun changeLike(postMetaData: PostMetaData)
    fun changePostTopCommentLike(postId:String, commentMetaData: CommentMetaData)
    fun showRoomDialog(item: PostFeedCard)
    fun onVideoStateChange(postId: String, resume: Boolean)
    fun goTopic(tag: PostTag, postId: String)
    fun showShareDialog(item: PostFeedCard)
    fun goOutfit(item: PostFeedCard)
    fun goMoment(item: PostMomentCard)
    fun goUgcDesign(item: PostFeedCard, ugcDesign: PostUgcDesignCard)
    fun clickLabel(data: Pair<Int, LabelInfo?>)
    fun changeFollow(uuid: String, follow: Boolean, followFrom: CommunityFollowFrom)
    fun moreFollowingUsers() {}
    fun moreFollowedWorks() {}
    fun clickFollowedWork(game: CreatorUgcGame) {}
    fun onFollowedWorkVisibilityChange(game: CreatorUgcGame, visible: Boolean) {}
    fun onFollowingUserVisibilityChange(user: KolCreatorInfo, visible: Boolean) {}
}

private val dp05 by lazy { 0.5.dp }
private val dp3 by lazy { 3.dp }
private val dp4 by lazy { 4.dp }
private val dp6 by lazy { 6.dp }
private val dp8 by lazy { 8.dp }
private val dp10 by lazy { 10.dp }
private val dp12 by lazy { 12.dp }
private val dp14 by lazy { 14.dp }
private val dp16 by lazy { 16.dp }
private val dp20 by lazy { 20.dp }
private val dp56 by lazy { 56.dp }
private val dp72 by lazy { 72.dp }
private const val ROOM_CARD_RATIO = 190 / 164F

private class TopicSquareCarouselModel() : CarouselModel_() {
    override fun buildView(parent: ViewGroup): VerticalCarousel {
        val v = VerticalCarousel(parent.context)
        v.background = ContextCompat.getDrawable(v.context, R.drawable.bg_topics_square_card)
        return v
    }
}

class Grid2VerticalCarouselV2(context: Context) : Carousel(context) {
    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(context, 2, GridLayoutManager.VERTICAL, false)
    }
}

class HorizontalCarouselCarouselModel() : CarouselModel_() {
    override fun buildView(parent: ViewGroup): HorizontalCarousel {
        return HorizontalCarousel(parent.context).apply {
            // 默认是WRAP_CONTENT, 这里改为MATCH_PARENT
            layoutParams = MarginLayoutParams(MarginLayoutParams.MATCH_PARENT, MarginLayoutParams.WRAP_CONTENT)
        }
    }
}

class ImageGridCarouselCarouselModel() : CarouselModel_() {
    private var _recyclerView: RecyclerView? = null
    val recyclerView get() = _recyclerView
    override fun buildView(parent: ViewGroup): ImageCarouselModel {
        val model = ImageCarouselModel(parent.context)
        _recyclerView = model
        return model
    }

    override fun onBind(listener: OnModelBoundListener<CarouselModel_, Carousel>?): CarouselModel_ {
        return super.onBind(listener)
    }
}

class VerticalCarousel(context: Context) : Carousel(context) {
    override fun createLayoutManager(): LayoutManager {
        return LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
    }
}

class HorizontalCarousel(context: Context) : Carousel(context) {
    override fun createLayoutManager(): LayoutManager {
        return LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
    }
}

class ImageCarouselModel(context: Context) : Carousel(context) {
    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(context, 6, LinearLayoutManager.VERTICAL, false)
    }
}

/**
 * 单个帖子
 * @param contentWidth 内容宽度（不包含左右空隙）
 * @param item 帖子数据
 * @param itemPosition 帖子数据排序位置（不是布局）
 * @param showUserStatus 是否展示发帖人用户状态
 * @param showPin 是否展示帖子置顶状态
 * @param firstPaddingTop 第一个帖子的顶部间距
 * @param showTagList 是否展示tag列表
 * @param feedListener 事件回调
 */

@SuppressLint("ClickableViewAccessibility")
fun MetaEpoxyController.communityFeedV2(
    contentWidth: Int,
    item: PostFeedCard,
    itemPosition: Int,
    showUserStatus: Boolean,
    showPin: Boolean,
    firstPaddingTop: Int,
    fragment: Fragment,
    playLikeAnimResId: String? = null,
    currentUserId: String,
    feedListener: ICommunityFeedListenerV2
) {
    // 头部
    val uniqueId = item.localUniqueId
    add(
        CommunityFeedHeadItemV2(
            item,
            contentWidth,
            showUserStatus,
            showPin,
            if (itemPosition == 0) firstPaddingTop else dp16,
            fragment,
            currentUserId,
            feedListener
        ).id("CommunityFeedHeadItem-[$uniqueId]")
    )
    val avatarSpaceWidth = dp56
    if (!item.firstVideo?.resPath.isNullOrEmpty()) {
        // 视频
        add(
            CommunityFeedItemVideoV2(
                item, uniqueId, contentWidth - avatarSpaceWidth, itemPosition, fragment, feedListener
            ).id("CommunityFeedItem-[$uniqueId]")
        )
    } else if (!item.imageList.isNullOrEmpty()) {
        // 图片
        val totalImages = item.imageList!!.filter { it.resPath.isNotEmpty() }
        val images = totalImages.take(9)

        if (totalImages.isNotEmpty()) {
            if (totalImages.size == 1) {
                // 单图
                add {
                    CommunityFeedItemSingleImage(
                        contentWidth = contentWidth - avatarSpaceWidth,
                        totalImageList = totalImages,
                        imageInfo = totalImages[0],
                        localPublishing = item.localPublishing,
                        position = 0,
                        glide = feedListener.getGlideOrNull(),
                        openImagePreview = {
                            val url = totalImages[0].resPath
                            feedListener.openImagePreview(listOf(url), it, 0)
                        },
                        rootClicked = { feedListener.goPost(item, null) }
                    ).id("CommunityFeedItemImage-[$uniqueId]-single")
                }
            } else {
                // 多图
                add(
                    CommunityGridImageItem(
                        images = images,
                        contentWidth = contentWidth - avatarSpaceWidth,
                        glide = feedListener.getGlideOrNull(),
                        localPublishing = item.localPublishing,
                        openImagePreview = { imageViews, pos ->
                            feedListener.openImagePreview(images.map { it.resPath }, imageViews, pos)
                        },
                        rootClicked = { feedListener.goPost(item, null) }
                    ).id("CommunityFeedItemImageRvItem-[$uniqueId]")
                )
            }
        }
    }

    // 最多展示3张卡片
    val cardList = item.cardList.take(3)
    if (cardList.isNotEmpty()) {
        cardList.forEachIndexed { index, card ->
            add {
                CommunityFeedAssetsCardV2(
                    item,
                    card,
                    feedListener
                ).id("CommunityFeedAssetsCard-${item.postId}-item-$index")
            }
        }
    }

    // 尾部
    add(
        CommunityFeedFootItemV2(
            item,
            item.postId == playLikeAnimResId,
            showBottomDivider = item.commentInfo == null,
            feedListener
        ).id("CommunityFeedFootItem-[$uniqueId]")
    )
    if (item.commentInfo != null) {
        // 置顶评论
        add {
            CommunityFeedItemTopComment(
                item,
                item.commentInfo,
                item.commentInfo.commentId == playLikeAnimResId,
                feedListener
            ).id("CommunityFeedItemTopComment-[$uniqueId]-${item.postId}-${item.commentInfo.commentId}")
        }
    }
}

// 顶部信息、文本
data class CommunityFeedHeadItemV2(
    val item: PostFeedCard,
    val contentWidth: Int,
    val showUserStatus: Boolean,
    val showPin: Boolean,
    val paddingTop: Int,
    val fragment: Fragment,
    val currentUserId: String,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityFeedHeadBinding>(
    R.layout.item_community_feed_head,
    ItemCommunityFeedHeadBinding::bind
) {
    override fun ItemCommunityFeedHeadBinding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        vMask.isVisible = item.localPublishing

        // 当前是主态或者关注中的状态, 就隐藏关注按钮
        if (item.uid == currentUserId || item.user?.followStatus == true) {
            // 主态
            ivFollow.invisible()
            if (!lavFollowAnim.isAnimating) {
                lavFollowAnim.gone()
            }
            vFollowClick.gone()
            vFollowClick.unsetOnClick()
        } else {
            // 客态
            lavFollowAnim.setMinAndMaxProgress(0.0f, 1.0f)
            ivFollow.visible()
            lavFollowAnim.cancelAnimationIfAnimating()
            lavFollowAnim.gone()
            if (!item.uid.isNullOrEmpty()) {
                vFollowClick.visible()
                vFollowClick.setOnAntiViolenceClickListener {
                    feedListener.changeFollow(
                        item.uid,
                        true,
                        CommunityFollowFrom.FEED
                    )
                    vFollowClick.gone()
                    ivFollow.invisible()
                    lavFollowAnim.visible()
                    lavFollowAnim.progress = 0f
                    lavFollowAnim.playAnimation()
                }
            }
        }
        val user = item.user
        tvName.text = user?.nickname ?: ""
        ivPortrait.setMargin(top = paddingTop)
        feedListener.getGlideOrNull()?.run {
            load(user?.avatar).placeholder(R.drawable.icon_default_avatar)
                .into(ivPortrait)
        }
        tvTime.text = DateUtilWrapper.getCreateFormatDate(
            context,
            item.createTime ?: System.currentTimeMillis()
        )
        val content = CommunityUtil.parsePostContent(
            root.context,
            item.content,
            item.tagList,
            item.communityTagList,
        ) { tag ->
            Timber.tag(POST_TAG).d("tvContent spannable")
            if (item.postId != null) {
                feedListener.goTopic(tag, item.postId)
            }
        }
        if (content.isNullOrEmpty()) {
            tvContent.gone()
        } else {
            tvContent.visible()
            tvContent.setOnShrinkingCallback { start ->
                val tempSetPre = HashSet<String>()
                val tempSetPost = HashSet<String>()
                item.communityTagList?.forEach {
                    if (it.index + it.tagName.length < start) {
                        tempSetPre.add(it.tagName)
                    } else if (!tempSetPre.contains(it.tagName)) {
                        tempSetPost.add(it.tagName)
                    }
                }
                val filteredTags = item.tagList?.filter {
                    tempSetPost.contains(it.tagName)
                }
                if (filteredTags.isNullOrEmpty()) {
                    null
                } else {
                    parseTagList(
                        root.context,
                        filteredTags,
                        "\n"
                    ) { tag ->
                        if (item.postId != null) {
                            feedListener.goTopic(tag, item.postId)
                        }
                    }
                }
            }
            tvContent.updateForRecyclerView(
                content,
                contentWidth,
                STATE_SHRINK
            )
        }
        ivCertification.show(
            user?.tagIds,
            user?.labelInfo,
            isOfficial = item.user?.isOfficial == true,
            glide = feedListener.getGlideOrNull()
        )
        PostHelper.updateReviewStatusForIcon(tvTime, item.status)
        val gameStatus = item.userStatus.toLocalStatus()
        when {
            !showUserStatus -> {
                ivState.isVisible = false
            }

            item.canGoRoom && item.userStatus != null -> {
                ivState.isVisible = true
            }

            gameStatus == FriendStatus.ONLINE || gameStatus == FriendStatus.PLAYING_GAME -> {
                ivState.isVisible = true
            }

            else -> {
                ivState.isVisible = false
            }
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        tvContent.setOnAntiViolenceClickListener {
            Timber.tag(POST_TAG).d("tvContent onClick")
            feedListener.goPost(item)
        }
        layerUser.setOnAntiViolenceClickListener {
            if (item.canGoRoom && showUserStatus) {
                feedListener.showRoomDialog(item)
            } else {
                if (!item.uid.isNullOrEmpty()) {
                    feedListener.goProfile(item.uid, "communityFeed")
                }
            }
        }
        ivCertification.setListener {
            feedListener.clickLabel(it)
        }
    }

    override fun ItemCommunityFeedHeadBinding.onUnbind() {
        ivCertification.setListener(null)
        vFollowClick.unsetOnClick()
        root.unsetOnClick()
        tvContent.unsetOnClick()
        layerUser.unsetOnClick()
    }

    private fun parseTagList(
        context: Context,
        tagList: List<PostTag>?,
        prefix: CharSequence?,
        tagClickCallback: (PostTag) -> Unit
    ): CharSequence? {
        if (tagList.isNullOrEmpty()) return null
        val builder = SpannableHelper.Builder()
        if (!prefix.isNullOrEmpty()) {
            builder.text(prefix)
        }
        // 去重
        tagList.distinctBy { it.tagId }.forEachIndexed { index, postTag ->
            builder.text(context.getString(R.string.community_hashtag_prefix, postTag.tagName))
                .click(CommunityTagSpannable(context, postTag, tagClickCallback))
            // 防止点击spannable后面空白区域也会触发link click，文本最后加了一个空格
            builder.text(" ")
        }
        return builder.build()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onItemVisibilityChange(
                item,
                "feed-head",
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }
}

// 视频
data class CommunityFeedItemVideoV2(
    val item: PostFeedCard,
    val uniqueId: String,
    val contentWidth: Int,
    val position: Int,
    val fragment: Fragment,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityFeedVideoBinding>(
    R.layout.item_community_feed_video,
    ItemCommunityFeedVideoBinding::bind
) {
    override fun ItemCommunityFeedVideoBinding.onBind() {
        root.children.forEach {
            it.enableWithAlpha(!item.localPublishing, 0.5F)
        }
        vMask.isVisible = item.localPublishing

        val videoInfo = item.firstVideo!!
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        pageListView.apply {
            val videoPath = videoInfo.resPath
            setDataResource(PlayerVideoResource(videoPath, ""))
            currentFragment = fragment
            FeedImageVideoUtil.setVideoSize(
                spaceNotReady,
                pageListView,
                videoInfo,
                videoPath,
                contentWidth,
                feedListener.getGlideOrNull()
            )
            if (!item.postId.isNullOrEmpty()) {
                setOnClickFullScreen {
                    feedListener.showVideoDetail(item.postId, videoPath)
                }
                setOnVideoResume {
                    feedListener.onVideoStateChange(item.postId, true)
                }
                setOnVideoPause {
                    feedListener.onVideoStateChange(item.postId, false)
                }
            }
        }
    }

    override fun ItemCommunityFeedVideoBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun onVisibilityChanged(
        percentVisibleHeight: Float,
        percentVisibleWidth: Float,
        visibleHeight: Int,
        visibleWidth: Int,
        view: View
    ) {
        super.onVisibilityChanged(
            percentVisibleHeight,
            percentVisibleWidth,
            visibleHeight,
            visibleWidth,
            view
        )
        feedListener.calculateVideoPlay(
            uniqueId,
            item.firstVideo!!.resPath,
            position,
            view.findViewById(R.id.pageListView),
            percentVisibleHeight
        )
    }
}

data class CommunityFeedItemSingleImage(
    val contentWidth: Int,
    val topSpace: Int = dp6,
    val startSpace: Int = dp72,
    val gridSpanSize: Int = 1,
    val totalImageList: List<PostMediaResource>,
    val imageInfo: PostMediaResource,
    val localPublishing: Boolean,
    val position: Int,
    val glide: RequestManager?,
    val showImageDetail: ((medias: List<PostMediaResource>, position: Int) -> Unit)? = null,
    val openImagePreview: ((List<ImageView>) -> Unit)? = null,
    val rootClicked: (View) -> Unit,
) : ViewBindingItemModel<ItemCommunityFeedSingleImageBinding>(
    R.layout.item_community_feed_single_image,
    ItemCommunityFeedSingleImageBinding::bind
) {
    override fun ItemCommunityFeedSingleImageBinding.onBind() {
        spaceTop.setHeight(topSpace)
        spaceStart.setWidth(startSpace)
        root.children.forEach {
            it.enableAllWithAlpha(!localPublishing, 0.5F)
        }
        vMask.isVisible = localPublishing
        root.setOnAntiViolenceClickListener(rootClicked)
        // 优先使用缩略图
        val imagePath = imageInfo.thumb.ifNullOrEmpty { imageInfo.resPath }
        val isLongLongImage = imageInfo.resourceHeight > 0
                && imageInfo.resourceWidth > 0
                && imageInfo.resourceHeight / imageInfo.resourceWidth >= 3
        if (imageInfo.resourceWidth > 0 && imageInfo.resourceHeight > 0) {
            // 有图片尺寸信息
            FeedImageVideoUtil.scaleImageSingle(
                false,
                true,
                ivImage,
                contentWidth,
                imageInfo.resourceWidth,
                imageInfo.resourceHeight,
                dp16,
                dp16
            )
            glide?.run {
                val glide = if (imagePath.isHttp()) {
                    load(imagePath)
                } else {
                    load(imagePath).skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.placeholder(R.drawable.placeholder_corner_6)

                if (isLongLongImage) {
                    glide.transform(MaxSizeCrop(maxHeight = ivImage.layoutParams.height))
                } else {
                    glide.centerCrop()
                }.into(ivImage)
            }
        } else {
            glide?.run {
                // 没有宽高的图
                if (imagePath.isHttp()) {
                    asBitmap()
                } else {
                    asBitmap().skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.load(imagePath).into(object : SimpleTarget<Bitmap?>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap?>?
                    ) {
                        FeedImageVideoUtil.scaleImageSingle(
                            false,
                            true,
                            ivImage,
                            contentWidth,
                            resource.width,
                            resource.height,
                            dp16,
                            dp16
                        )
                        ivImage.setImageBitmap(resource)
                    }
                })
            }
        }
        tvLongImage.visible(isLongLongImage)
        ivImage.setOnAntiViolenceClickListener {
            if(openImagePreview != null) openImagePreview.invoke(listOf(ivImage))
            else showImageDetail?.invoke(totalImageList, 0)
        }
    }

    override fun getSpanSize(totalSpanCount: Int, position: Int, itemCount: Int): Int {
        return gridSpanSize
    }

    override fun ItemCommunityFeedSingleImageBinding.onUnbind() {
        root.unsetOnClick()
        ivImage.unsetOnClick()
    }
}

// 图片9宫格布局
data class CommunityGridImageItem(
    val images: List<PostMediaResource>,
    val contentWidth: Int,
    val localPublishing: Boolean,
    val glide: RequestManager?,
    val openImagePreview: (List<ImageView>, Int) -> Unit,
    val rootClicked: (View) -> Unit,
) : ViewBindingItemModel<LayoutCommunityGridImageBinding>(
    R.layout.layout_community_grid_image,
    LayoutCommunityGridImageBinding::bind
) {
    override fun LayoutCommunityGridImageBinding.onBind() {
        val imageViews = listOf(
            icImage1, icImage2,
            icImage3, icImage4,
            icImage5, icImage6,
            icImage7, icImage8,
            icImage9
        )

        val visible = showImages(imageViews)
        images.forEachIndexed { index, imageInfo ->
            val imageView = imageViews[index]

            root.enableAllWithAlpha(!localPublishing, 0.5F)
            imageView.vMask.isVisible = localPublishing
            root.setOnAntiViolenceClickListener(rootClicked)

            imageView.ivImage.setOnAntiViolenceClickListener {
                openImagePreview(visible, index)
            }

            // 优先使用缩略图
            val imagePath = imageInfo.thumb.ifNullOrEmpty { imageInfo.resPath }
            glide?.run {
                val glide = if (imagePath.isHttp()) {
                    load(imagePath)
                } else {
                    load(imagePath).skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                }.placeholder(R.drawable.placeholder_corner_6)

                val isLongLongImage = imageInfo.resourceHeight > 0
                        && imageInfo.resourceWidth > 0
                        && imageInfo.resourceHeight / imageInfo.resourceWidth >= 3
                if (isLongLongImage) {
                    glide.transform(MaxSizeCrop(maxHeight = imageView.ivImage.layoutParams.height))
                } else {
                    glide.centerCrop()
                }.into(imageView.ivImage)
                imageView.tvLongImageMarker.visible(isLongLongImage)
            }
        }
    }

    private fun showImages(imageViews: List<ItemCommunityGridImagesBinding>): List<ImageView> {
        val size = images.size
        val visibleImageViews = mutableListOf<ImageView>()
        imageViews.forEachIndexed { index, imageView ->
            if (index < size) {
                imageView.root.visible()
                visibleImageViews.add(imageView.ivImage)
            } else {
                imageView.root.gone()
            }

        }
        return visibleImageViews
    }

    override fun LayoutCommunityGridImageBinding.onUnbind() {
        root.unsetOnClick()
        listOf(
            icImage1, icImage2,
            icImage3, icImage4,
            icImage5, icImage6,
            icImage7, icImage8,
            icImage9
        ).forEach {
            it.ivImage.unsetOnClick()
        }
    }
}

data class CommunityFeedAssetsCardV2(
    val item: PostFeedCard,
    /**
     * item 的类型可能有以下类型:
     * 游戏卡片: PostCardInfo
     * 拍剧卡片: PostMomentCard
     * 穿搭卡片: PostStyleCard
     * 服装卡片: PostUgcDesignCard
     */
    val card: Any,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityAssetsCardBinding>(
    R.layout.item_community_assets_card,
    ItemCommunityAssetsCardBinding::bind
) {
    override fun ItemCommunityAssetsCardBinding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        ivDelBtn.gone()
        val defaultBgColor = getColorByRes(R.color.color_97BCDE)
        if (card is PostCardInfo) {
            // 游戏卡片
            layoutMetaLike.visible()
            mlvLike.visible()
            mlvPlayers.visible()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.gameIcon).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.gameName
            mlvLike.setLikeText(UnitUtil.formatKMCount(card.likeCount))
            mlvPlayers.setLikeText(UnitUtil.formatKMCount(card.player))
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goGameDetail(item, card)
            }
        } else if (card is PostMomentCard) {
            // 拍剧卡片
            layoutMetaLike.gone()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.materialUrl).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.templateName
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goMoment(card)
            }
        } else if (card is PostStyleCard) {
            // 穿搭卡片
            layoutMetaLike.visible()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.visible()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.wholeBodyImage).centerCrop()
                    .placeholder(R.drawable.avatar_friend_placeholder)
                    .into(ivIcon)
            }
            tvTitle.setTextWithArgs(
                R.string.s_outfit,
                item.user?.nickname.orEmpty()
            )
            mlvTryOn.setLikeText(getString(R.string.tried_on, card.pvStr))
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goOutfit(item)
            }
        } else if (card is PostUgcDesignCard) {
            // 服装卡片
            layoutMetaLike.visible()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            feedListener.getGlideOrNull()?.apply {
                load(card.cover).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.title.ifNullOrEmpty { getString(R.string.fashion_design) }
            bgCard.setOnAntiViolenceClickListener {
                feedListener.goUgcDesign(item, card)
            }
        }
    }

    override fun ItemCommunityAssetsCardBinding.onUnbind() {
        root.unsetOnClick()
        bgCard.unsetOnClick()
    }
}

// 底部卡片、tag、态度、分割线
data class CommunityFeedFootItemV2(
    val item: PostFeedCard,
    val playLikeAnim: Boolean,
    val showBottomDivider: Boolean,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityFeedFootV2Binding>(
    R.layout.item_community_feed_foot_v2,
    ItemCommunityFeedFootV2Binding::bind
) {

    override fun ItemCommunityFeedFootV2Binding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        vMask.isVisible = item.localPublishing
        vLine.visible(showBottomDivider)

        // attitude
        includeAttitude.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)
        if (item.opinionLike) {
            includeAttitude.ivLike.invisible()
            includeAttitude.lavLikeAnim.visible()
            if (!includeAttitude.lavLikeAnim.isAnimating) {
                includeAttitude.lavLikeAnim.progress = 1f
            }
        } else {
            includeAttitude.ivLike.visible()
            includeAttitude.lavLikeAnim.cancelAnimationIfAnimating()
            includeAttitude.lavLikeAnim.gone()
        }
        includeAttitude.tvLike.text = UnitUtil.formatKMCount(item.likeCount ?: 0)
        includeAttitude.tvLike.setTextColorByRes(if (item.opinionLike) R.color.color_FF5F42 else R.color.color_666666)
        includeAttitude.tvComment.text = UnitUtil.formatKMCount(item.commentCount ?: 0)
        includeAttitude.tvShare.text = UnitUtil.formatKMCount(item.shareCount ?: 0)
        includeAttitude.layerLike.setOnAntiViolenceClickListener {
            if (!item.postId.isNullOrEmpty()) {
                feedListener.changeLike(item.getPostMetaData())
                val targetLike = !item.opinionLike
                if (targetLike) {
                    includeAttitude.ivLike.invisible()
                    includeAttitude.lavLikeAnim.visible()
                    includeAttitude.lavLikeAnim.progress = 0f
                    includeAttitude.lavLikeAnim.playAnimation()
                } else {
                    includeAttitude.ivLike.visible()
                    includeAttitude.lavLikeAnim.gone()
                }
            }
        }
        includeAttitude.layerComment.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
        includeAttitude.layerShare.setOnAntiViolenceClickListener {
            feedListener.showShareDialog(item)
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item)
        }
    }

    override fun ItemCommunityFeedFootV2Binding.onUnbind() {
        includeAttitude.lavLikeAnim.cancelAnimation()
        includeAttitude.lavLikeAnim.progress = 0f
        root.unsetOnClick()
        includeAttitude.layerLike.unsetOnClick()
        includeAttitude.layerComment.unsetOnClick()
        includeAttitude.layerShare.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onItemVisibilityChange(
                item,
                "foot",
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }
}

data class CommunityFeedItemTopComment(
    val item: PostFeedCard,
    val commentInfo: CommentInfo,
    val playLikeAnim: Boolean,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityFeedHotCommentBinding>(
    R.layout.item_community_feed_hot_comment,
    ItemCommunityFeedHotCommentBinding::bind
) {
    override fun ItemCommunityFeedHotCommentBinding.onBind() {
        root.children.forEach {
            it.enableAllWithAlpha(!item.localPublishing, 0.5F)
        }
        root.setOnAntiViolenceClickListener {
            feedListener.goPost(item, commentInfo)
        }
        feedListener.getGlideOrNull()?.apply {
            load(commentInfo.user?.avatar)
                .placeholder(R.drawable.icon_default_avatar)
                .into(ivPortrait)
        }
        tvName.text = commentInfo.user?.nickname ?: ""
        labelView.show(
            commentInfo.user?.tagIds,
            commentInfo.user?.labelInfo,
            isOfficial = item.user?.isOfficial == true,
            glide = feedListener.getGlideOrNull()
        )

        lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)
        if (commentInfo.opinionLike) {
            ivLike.invisible()
            lavLikeAnim.visible()
            if (!lavLikeAnim.isAnimating) {
                lavLikeAnim.progress = 1f
            }
        } else {
            ivLike.visible()
            lavLikeAnim.cancelAnimationIfAnimating()
            lavLikeAnim.gone()
        }
        tvLike.text = UnitUtil.formatKMCount(commentInfo.likeCount ?: 0)
        tvLike.setTextColorByRes(if (commentInfo.opinionLike) R.color.color_FF5F42 else R.color.color_666666)
        layerLike.setOnAntiViolenceClickListener {
            if (!item.postId.isNullOrEmpty() && !commentInfo.commentId.isNullOrEmpty()) {
                feedListener.changePostTopCommentLike(
                    item.postId,
                    CommentMetaData(
                        commentId = commentInfo.commentId,
                        commentLiked = commentInfo.opinionLike,
                        commentLikeCount = commentInfo.likeCount ?: 0,
                    )
                )
                val targetLike = !item.opinionLike
                if (targetLike) {
                    ivLike.invisible()
                    lavLikeAnim.visible()
                    lavLikeAnim.progress = 0f
                    lavLikeAnim.playAnimation()
                } else {
                    ivLike.visible()
                    lavLikeAnim.gone()
                }
            }
        }

        if(commentInfo.content.isNullOrEmpty()) {
            tvContent.gone()
        } else {
            tvContent.visible()
            tvContent.text = commentInfo.content
        }
        val imageList = commentInfo.imageList
        if (imageList.isNullOrEmpty()) {
            imageTag.gone()
        } else {
            imageTag.visible()
            imageTag.setOnAntiViolenceClickListener {
               feedListener.showImageDetail(imageList.map { it.resPath }, 0)
            }
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onItemVisibilityChange(
                item,
                "topComment",
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }

    override fun ItemCommunityFeedHotCommentBinding.onUnbind() {
        root.unsetOnClick()
        layerLike.unsetOnClick()
        imageTag.unsetOnClick()
    }
}

fun MetaEpoxyController.communityTopicsV2(
    item: TopicFeedCard,
    index: Int,
    topicListener: ICommunityTopicListenerV2
) {
    val topics = item.list
    if (topics.isNullOrEmpty()) {
        return
    }
    carouselBuilder(EpoxyCarouselBuilder(TopicSquareCarouselModel())) {
        id("CommunityTopicsSquare-$index")
        padding(Carousel.Padding(0, dp8, 0, 0, 0))
        add(
            CommunityTopicsSquareTitle(
                item,
                topicListener
            ).id("CommunityTopicsSquare-title-$index")
        )
        topics.forEachIndexed { position, topic ->
            add(
                CommunityTopicsSquareItem(
                    topic,
                    position,
                    topicListener
                ).id("CommunityTopicsSquare-item-$index-${topic.tagId}")
            )
        }
        spacer(height = dp8)
        divider(height = dp05, colorRes = R.color.color_F0F0F0, id = index.toLong())
    }
}

data class CommunityTopicsSquareTitle(
    val item: TopicFeedCard,
    val topicListener: ICommunityTopicListenerV2
) : ViewBindingItemModel<ItemCommunityFeedTopicsSquareTitleBinding>(
    R.layout.item_community_feed_topics_square_title,
    ItemCommunityFeedTopicsSquareTitleBinding::bind
) {
    override fun ItemCommunityFeedTopicsSquareTitleBinding.onBind() {
        root.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.COMMUNITY_TAG_SQUARE)
            topicListener.goTopicSquare()
        }
    }

    override fun ItemCommunityFeedTopicsSquareTitleBinding.onUnbind() {
        root.unsetOnClick()
    }
}

data class CommunityTopicsSquareItem(
    val item: Topic,
    val position: Int,
    val topicListener: ICommunityTopicListenerV2
) : ViewBindingItemModel<ItemCommunityFeedTopicsSquareItemBinding>(
    R.layout.item_community_feed_topics_square_item,
    ItemCommunityFeedTopicsSquareItemBinding::bind
) {
    override fun ItemCommunityFeedTopicsSquareItemBinding.onBind() {
        tvTopicTitle.text = getString(R.string.community_hashtag_prefix, item.tagName ?: "")
        ivTopicHot.visible(item.hot == true)
        tvTopicDesc.setTextWithArgs(
            R.string.community_topic_item_view_flowing_count,
            UnitUtil.formatNumberWithUnit(
                num = item.viewCount ?: 0,
                decimal = 1,
                showRawNumberBelow10W = false
            ),
            UnitUtil.formatNumberWithUnit(
                num = item.followCount ?: 0,
                decimal = 1,
                showRawNumberBelow10W = false
            )
        )
        root.setOnAntiViolenceClickListener {
            topicListener.goTopicDetail(item)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            topicListener.trackTopicRankShow(item, position + 1)
        }
    }

    override fun ItemCommunityFeedTopicsSquareItemBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaEpoxyController.communityRecommendCreatorV2(
    users: List<UserFeedUser>?,
    followFrom: CommunityFollowFrom,
    index: Int,
    uniqueTag: Int,
    listener: ICommunityRecommendUsersCard
) {
    if (users.isNullOrEmpty()) {
        return
    }
    val communityRecommendCreatorTitle = CommunityRecommendCreatorTitle()
    add(
        communityRecommendCreatorTitle.id("CommunityRecommendCreator-title-$index-$uniqueTag")
    )
    carouselNoSnapWrapBuilder {
        id("CommunityRecommendCreator-$index-$uniqueTag")
        padding(Carousel.Padding.dp(16, 0, 6, 0, 0))
        users.forEach { user ->
            add(
                CommunityRecommendCreatorV2(
                    user,
                    followFrom,
                    listener
                ).id("CommunityRecommendCreator-item-$index-${user.uid}-$uniqueTag")
            )
        }
        add(
            CommunityRecommendCreatorMore(
                listener
            ).id("CommunityRecommendCreatorMore-item-$index")
        )
        onBind { model, view, position ->
            view ?: return@onBind
            view.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    val offset = recyclerView.computeHorizontalScrollOffset()
                    val range = recyclerView.computeHorizontalScrollRange()
                    val extent = recyclerView.computeHorizontalScrollExtent()
                    val percent = if (extent >= range) {
                        1.0f
                    } else {
                        offset.toFloat() / (range - extent)
                    }
                    // EpoxyRecyclerView 是根据数据驱动界面的, 然后依据diff算法更新View
                    // 滚动进度条会频繁更新进度, 为了减少更新计算量, 这里直接操作View去更新进度
                    communityRecommendCreatorTitle.updateProgress(percent)
                }
            })
        }
        onUnbind { model, view ->
            view.clearOnScrollListeners()
            communityRecommendCreatorTitle.updateProgress(0f)
        }
    }
    spacer(height = dp16)
    divider(height = dp05, colorRes = R.color.color_F0F0F0)
}

class CommunityRecommendCreatorTitle : ViewBindingItemModel<ItemCommunityFeedSuggestedUserTitleBinding>(
    R.layout.item_community_feed_suggested_user_title,
    ItemCommunityFeedSuggestedUserTitleBinding::bind
) {
    private var scrollFgProgress: View? = null
    fun updateProgress(progress: Float) {
        // 进度条背景宽度为dp24, 前景色宽度为dp8, 所以还剩dp16的范围可以滑动
        val maxTranslate = dp16
        scrollFgProgress?.translationX = maxTranslate * progress
    }

    override fun ItemCommunityFeedSuggestedUserTitleBinding.onBind() {
        scrollFgProgress = fgProgress
    }
}

data class CommunityRecommendCreatorV2(
    val item: UserFeedUser,
    val followFrom: CommunityFollowFrom,
    val feedListener: ICommunityRecommendUsersCard,
) : ViewBindingItemModel<AdapterRecommendCreatorBinding>(
    R.layout.adapter_recommend_creator,
    AdapterRecommendCreatorBinding::bind
) {
    override fun AdapterRecommendCreatorBinding.onBind() {
        feedListener.getGlideOrNull()?.apply {
            load(item.avatar)
                .placeholder(R.drawable.placeholder_icon)
                .into(iv)
        }
        vUserClick.setOnAntiViolenceClickListener {
            if (item.uid != null) {
                feedListener.clickAvatar(item)
            }
        }
        val showLabelView = labelView.show(
            item.tagIds,
            item.userLabelDTO?.toLabelInfo(),
            glide = feedListener.getGlideOrNull(),
            upOffset = 0
        )
        labelViewBg.visible(showLabelView)
        tvUserName.text = item.nickname ?: ""
        tvSignature.text = item.signature ?: ""
        followView.style = if (item.followStatus == true) {
            FollowView.Style.HOLLOW
        } else {
            FollowView.Style.SOLID
        }
        followView.status = if (item.followStatus == true) {
            FollowView.Status.FOLLOWING
        } else {
            FollowView.Status.UNFOLLOW
        }
        if (!item.uid.isNullOrEmpty()) {
            vFollowClick.setOnAntiViolenceClickListener {
                feedListener.changeFollow(item, followFrom)
            }
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            feedListener.onUserCardItemVisible(item, followFrom)
        }
    }

    override fun AdapterRecommendCreatorBinding.onUnbind() {
        vUserClick.unsetOnClick()
        vFollowClick.unsetOnClick()
    }
}

data class CommunityRecommendCreatorMore(
    val listener: ICommunityRecommendUsersCard
) : ViewBindingItemModel<AdapterRecommendCreatorMoreBinding>(
    R.layout.adapter_recommend_creator_more,
    AdapterRecommendCreatorMoreBinding::bind
) {
    override fun AdapterRecommendCreatorMoreBinding.onBind() {
        vBg.setBackgroundResource(R.drawable.bg_round_12_gradient_26a7a8a9_to_white_stroke_05)
        root.setOnAntiViolenceClickListener {
            listener.clickMore()
        }
    }

    override fun AdapterRecommendCreatorMoreBinding.onUnbind() {
        root.unsetOnClick()
    }
}

data class CommunitySubListTitle(
    val title: String,
    val showMore: Boolean = true,
    val top: Int,
    val bottom: Int,
    val onMoreClicked: () -> Unit
) : ViewBindingItemModel<ItemCommunitySublistTitleBinding>(
    R.layout.item_community_sublist_title,
    ItemCommunitySublistTitleBinding::bind
) {
    override fun ItemCommunitySublistTitleBinding.onBind() {
        tvTitle.text = title
        tvMore.visible(showMore)
        if(showMore) {
            tvMore.setOnAntiViolenceClickListener {
                onMoreClicked()
            }
        }
        spaceTop.setHeight(top)
        spaceBottom.setHeight(bottom)
    }

    override fun ItemCommunitySublistTitleBinding.onUnbind() {
        tvMore.unsetOnClick()
    }
}

fun MetaEpoxyController.communityFollowingUsers(
    users: List<KolCreatorInfo>,
    index: Int,
    feedListener: ICommunityFeedListenerV2
) {
    if (users.isEmpty()) {
        return
    }
    add(
        CommunitySubListTitle(
            title = getStringByGlobal(R.string.community_following_users_title),
            showMore = true,
            top = dp16,
            bottom = dp16
        ) {
            feedListener.moreFollowingUsers()
        }.id("CommunitySubListTitle-followingUsers-$index")
    )
    // 默认情况下,CarouselNoSnapWrap 内部有手势的自定义处理, 会拦截ViewPager的左右滑动手势, 所以需要禁用内部的手势自定义处理
    carouselNoSnapWrapBuilder(EpoxyCarouselNoSnapWrapBuilder(CarouselNoSnapWrapModel(skipIntercept = true))) {
        id("CommunityFollowingUser-$index")
        padding(Carousel.Padding.dp(16, 0, 8, 0, 0))
        users.forEach { user ->
            add(
                CommunityFollowingUser(
                    user,
                    feedListener
                ).id("CommunityFollowingUser-item-$index-${user.uuid}")
            )
        }
    }
}

data class CommunityFollowingUser(
    val item: KolCreatorInfo,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityFollowingUserBinding>(
    R.layout.item_community_following_user,
    ItemCommunityFollowingUserBinding::bind
) {
    override fun ItemCommunityFollowingUserBinding.onBind() {
        feedListener.getGlideOrNull()?.apply {
            load(item.avatar)
                .placeholder(R.drawable.icon_default_creator_yellow_size88)
                .into(ivAvatar)
        }
        ivAvatar.setOnAntiViolenceClickListener {
            feedListener.goProfile(item.uuid, EventConstants.From.FROM_KOL_CREATOR_TAB_FOLLOWED)
        }
        tvUserName.text = item.nickname ?: ""
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onFollowingUserVisibilityChange(
                item,
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }

    override fun ItemCommunityFollowingUserBinding.onUnbind() {
        ivAvatar.unsetOnClick()
    }
}

fun MetaEpoxyController.communityFollowedWorks(
    works: List<CreatorUgcGame>,
    index: Int,
    feedListener: ICommunityFeedListenerV2
) {
    if (works.isEmpty()) {
        return
    }
    add(
        CommunitySubListTitle(
            title = getStringByGlobal(R.string.community_followed_works_title),
            showMore = true,
            top = dp16,
            bottom = dp12
        ) {
            feedListener.moreFollowedWorks()
        }.id("CommunitySubListTitle-followedWorks-$index")
    )
    // 默认情况下,CarouselNoSnapWrap 内部有手势的自定义处理, 会拦截ViewPager的左右滑动手势, 所以需要禁用内部的手势自定义处理
    carouselNoSnapWrapBuilder(EpoxyCarouselNoSnapWrapBuilder(CarouselNoSnapWrapModel(skipIntercept = true))) {
        id("CommunityFollowedWork-$index")
        padding(Carousel.Padding.dp(16, 0, 8, 0, 0))
        works.forEach { item ->
            add(
                CommunityFollowedWork(
                    item,
                    feedListener
                ).id("CommunityFollowedWork-item-$index-${item.id}")
            )
        }
    }
}

data class CommunityFollowedWork(
    val item: CreatorUgcGame,
    val feedListener: ICommunityFeedListenerV2
) : ViewBindingItemModel<ItemCommunityFollowedWorkBinding>(
    R.layout.item_community_followed_work,
    ItemCommunityFollowedWorkBinding::bind
) {
    override fun ItemCommunityFollowedWorkBinding.onBind() {
        root.setOnAntiViolenceClickListener {
            feedListener.clickFollowedWork(item)
        }
        feedListener.getGlideOrNull()?.apply {
            load(item.banner)
                .placeholder(R.drawable.placeholder)
                .into(ivIcon)

            load(item.userIcon)
                .placeholder(R.drawable.icon_default_creator_yellow_size88)
                .into(ivAuthorAvatar)
        }
        mlvLike.setLikeText(UnitUtil.formatKMCount(item.likeCount ?: item.loveQuantity))
        mlvPlayers.setLikeText(UnitUtil.formatKMCount(item.pvCount))
        tvAuthorName.text = item.userName ?: ""
        tvName.text = item.ugcGameName

        when (item.gameShowTag) {
            CreatorUgcGame.GAME_SHOW_TAG_NEW -> {
                tvTag.visible()
                tvTag.setText(R.string.community_followed_work_tag_new)
                tvTag.setBackgroundResource(R.drawable.bg_followed_work_new)
            }

            CreatorUgcGame.GAME_SHOW_TAG_HOT -> {
                tvTag.visible()
                tvTag.setText(R.string.community_followed_work_tag_hot)
                tvTag.setBackgroundResource(R.drawable.bg_followed_work_hot)

            }

            CreatorUgcGame.GAME_SHOW_TAG_RECOMMEND -> {
                tvTag.visible()
                tvTag.setText(R.string.community_followed_work_tag_recommend)
                tvTag.setBackgroundResource(R.drawable.bg_followed_work_recommend)

            }

            else -> {
                tvTag.gone()
            }
        }

        val dp12f = dp12.toFloat()
        val corner = floatArrayOf(
            0f, 0f,
            0f, 0f,
            dp12f, dp12f,
            dp12f, dp12f,
        )
        val bg = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadii = corner
            setColor(
                runCatching {
                    item.backgroundColor?.toColorInt()
                }.getOrNull() ?: getColorByRes(R.color.color_97BCDE)
            )
        }
        val fg = GradientDrawable(
            Orientation.TOP_BOTTOM,
            intArrayOf(
                getColorByRes(R.color.black_20),
                getColorByRes(R.color.transparent)
            )
        ).apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadii = corner
        }
        bottomBg.background = LayerDrawable(arrayOf(bg, fg)).apply {
            setId(0, android.R.id.background) // 底层：底色
            setId(1, android.R.id.progress)   // 上层：渐变
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            feedListener.onFollowedWorkVisibilityChange(
                item,
                visibilityState == VisibilityState.VISIBLE
            )
        }
    }

    override fun ItemCommunityFollowedWorkBinding.onUnbind() {
        root.unsetOnClick()
    }
}
package com.socialplay.gpark.ui.gamedetail.dialog

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentLikeAndPlayerItemBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import kotlinx.parcelize.Parcelize

/**
 * 点赞和游玩列表Fragment参数
 */
@Parcelize
data class LikeAndPlayerItemFragmentArgs(
    val gameId: String,
    val type: String,
    val isOwner: Boolean = true,
    val gameType: Int,
    val likeCount: Int = 0,
    val playerCount: Int = 0
) : Parcelable

/**
 * 点赞和游玩列表Fragment
 */
class LikeAndPlayerItemFragment :
    BaseRecyclerViewFragment<FragmentLikeAndPlayerItemBinding>(R.layout.fragment_like_and_player_item) {
    private val args: LikeAndPlayerItemFragmentArgs by args()

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvLikeAndPlayer

    private val viewModel: LikeAndPlayerItemViewModel by fragmentViewModel()

    companion object {
        const val TYPE_LIKE = "like"
        const val TYPE_PLAYER = "player"

        fun newInstance(
            type: String,
            gameId: String,
            isOwner: Boolean = true,
            gameType: Int,
            likeCount: Int = 0,
            playerCount: Int = 0
        ) = LikeAndPlayerItemFragment().apply {
            arguments = LikeAndPlayerItemFragmentArgs(
                gameId,
                type,
                isOwner,
                gameType,
                likeCount,
                playerCount
            ).asMavericksArgs()
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentLikeAndPlayerItemBinding? {
        return FragmentLikeAndPlayerItemBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        viewModel.registerAsyncErrorToast(LikeAndPlayerItemModelState::refresh)
        viewModel.setupRefreshLoading(
            LikeAndPlayerItemModelState::refresh,
            binding.loading,
            null,
            emptyMsgBlock = {
                // 根据类型显示不同的空列表提示
                if (viewModel.isPlayerList()) {
                    getString(R.string.player_empty_tip)
                } else {
                    getString(R.string.like_empty_tip)
                }
            }
        ) {
            viewModel.refreshList()
        }

//        // 监听列表状态变化
//        viewModel.onEach(LikeAndPlayerItemModelState::list) { list ->
//            notifyListStateChange(list.isEmpty())
//        }
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        LikeAndPlayerItemModelState::list,
        LikeAndPlayerItemModelState::loadMore,
        LikeAndPlayerItemModelState::isOwner
    ) { list, loadMore, isOwner ->
        list.forEach { item ->
            likeAndPlayerItem(
                item,
                ::glide,
                onClickItem = { uuid ->
                    Analytics.track(
                        EventConstants.C_GAMEDETAIL_LIKE_LIST_CLICK,
                        "gameid" to args.gameId,
                        "user_type" to if (args.isOwner) "owner" else "visitor",
                        "creatortype" to args.gameType.toString(),
                        "page_type" to if (args.type == TYPE_LIKE) {
                            "like"
                        } else if (args.type == TYPE_PLAYER) {
                            "player"
                        } else {
                            ""
                        },
                        "click" to "profile",
                    )
                    MetaRouter.Profile.other(this@LikeAndPlayerItemFragment, uuid, "like_player")
                },
                onClickMessage = { uuid ->
                    Analytics.track(
                        EventConstants.C_GAMEDETAIL_LIKE_LIST_CLICK,
                        "gameid" to args.gameId,
                        "user_type" to if (args.isOwner) "owner" else "visitor",
                        "creatortype" to args.gameType.toString(),
                        "page_type" to if (args.type == TYPE_LIKE) {
                            "like"
                        } else if (args.type == TYPE_PLAYER) {
                            "player"
                        } else {
                            ""
                        },
                        "click" to "im",
                    )
                    // 处理私信按钮点击事件
                    MetaRouter.IM.gotoConversation(this@LikeAndPlayerItemFragment, uuid)
                },
                isOwner = isOwner // 传递isOwner参数
            )
        }

        if (list.isNotEmpty()) {
            // 判断是否显示游玩列表限制提示
            if (viewModel.isPlayerList()) {
                // 游玩列表只展示前50个
                val isLimitReached = list.size >= viewModel.maxSize
                loadMoreFooter(
                    loadMore,
                    showEnd = true,
                    newEndText = getString(R.string.player_list_limit_tip)
//                    endText = if (isLimitReached) getString(R.string.player_list_limit_tip) else null
                ) {
                    // 如果没有达到限制，则加载更多
                    if (!isLimitReached) {
                        viewModel.loadMoreList()
                    }
                }
            } else {
                // 点赞列表正常分页
                loadMoreFooter(
                    loadMore,
                    showEnd = true,
                    endText = getString(R.string.like_list_end_tip)
                ) {
                    // 如果没有达到限制，则加载更多
                    viewModel.loadMoreList()
                }
            }
        }
    }

    override fun invalidate() {
        // 不需要实现
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_LIKE_AND_PLAYER_ITEM

    override fun isEnableTrackPageExposure(): Boolean = false

//    /**
//     * 列表状态变化回调接口
//     */
//    interface OnListStateChangeListener {
//        fun onListStateChange(isEmpty: Boolean)
//    }
//
//    private var listStateChangeListener: OnListStateChangeListener? = null
//
//    /**
//     * 设置列表状态变化回调
//     */
//    fun setOnListStateChangeListener(listener: OnListStateChangeListener) {
//        listStateChangeListener = listener
//    }
//
//    /**
//     * 通知列表状态变化
//     */
//    private fun notifyListStateChange(isEmpty: Boolean) {
//        listStateChangeListener?.onListStateChange(isEmpty)
//    }
//
//    /**
//     * 获取列表是否为空
//     */
//    fun isListEmpty(): Boolean {
//        return viewModel.oldState.list.isEmpty()
//    }
}

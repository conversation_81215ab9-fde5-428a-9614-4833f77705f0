package com.socialplay.gpark.ui.outfit

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcAssetEntrance
import com.socialplay.gpark.data.model.outfit.UgcAssetProfile
import com.socialplay.gpark.data.model.outfit.UgcAssetProfileEntrance
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTag
import com.socialplay.gpark.databinding.FragmentProfileTabUgcDesignBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.editor.BaseEditorFragmentV2
import com.socialplay.gpark.ui.profile.home.ProfileViewModel
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.view.center.CenterLayoutManager
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.toast
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/05
 *     desc   :
 * </pre>
 */
@Parcelize
data class ProfileUgcDesignFragmentArgs(val isMe: Boolean, val uuid: String) : Parcelable

class ProfileUgcDesignFragment :
    BaseEditorFragmentV2<FragmentProfileTabUgcDesignBinding>(R.layout.fragment_profile_tab_ugc_design) {

    companion object {
        fun newInstance(isMe: Boolean, uuid: String): ProfileUgcDesignFragment {
            return ProfileUgcDesignFragment().apply {
                arguments = ProfileUgcDesignFragmentArgs(isMe, uuid).asMavericksArgs()
            }
        }
    }

    private val args by args<ProfileUgcDesignFragmentArgs>()
    private val vm: ProfileUgcDesignViewModel by fragmentViewModel()
    private val profileViewModel: ProfileViewModel by parentFragmentViewModel()

    private val tabController by lazy { buildTabController() }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private var spanSize = 4

    private var otherUid: String = ""

    private var loadingDialog: LoadingDialogFragment? = null

    private val itemListenerV2 = object : IProfileUgcAssetListener {
        override fun clickMore(item: UgcAssetProfileEntrance) {
            val s = vm.oldState
            Analytics.track(
                EventConstants.MY_LIBRARY_TAB_MORE_CLICK,
                "type" to (if (item.isDesign) 0 else 1)
            )
            MetaRouter.UgcDesign.assetList(
                this@ProfileUgcDesignFragment,
                item.entrance,
                s.uuid,
                args.isMe,
                item.msg,
                s.tagCode
            )
        }

        override fun clickAsset(item: UgcAssetProfile, position: Int) {
            if (item.published || args.isMe) {
                MetaRouter.UgcDesign.detail(
                    this@ProfileUgcDesignFragment,
                    item.itemId,
                    CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB,
                    needResult = true
                )
            } else {
                toast(R.string.ugc_asset_click_hidden_tips)
            }
        }

        override fun clickTab(item: UgcDesignProfileTag, position: Int) {
            vm.updateTabSelection(position, item.code) {
                binding.rvTab.smoothScrollToPosition(position)
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            ProfileUgcDesignState::entrances
        )
        tabController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        tabController.onSaveInstanceState(outState)
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentProfileTabUgcDesignBinding? {
        return FragmentProfileTabUgcDesignBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        otherUid = profileViewModel.oldState.uuid
        vm.updateUuid(otherUid)
        if (args.isMe) {
            vm.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
                val uuid = it?.uuid ?: return@observe
                if (otherUid != uuid) {
                    otherUid = uuid
                    vm.updateUuid(otherUid)
                }
            }
        }
        spanSize = if (isPad) {
            ((screenWidth - dp(24)) / dp(88)).coerceAtLeast(4)
        } else {
            4
        }
        vm.updateSize(spanSize)

        binding.loadingUgc.setVerticalBias(0.08f)
        if (args.isMe) {
            binding.rvTab.layoutManager =
                CenterLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            binding.rvTab.setController(tabController)
        } else {
            binding.rvTab.gone()
        }

        recyclerView.layoutManager = GridLayoutManager(requireContext(), spanSize)

        binding.loadingUgc.setEmptyBtnClick {
            Analytics.track(
                EventConstants.GUIDE_CREATE_ONE_MINUTE_CLICK
            )
            vm.getModuleTemplate()
        }

        setFragmentResultListenerByHostFragment(
            UgcDesignDetailFragment.TAG,
            viewLifecycleOwner
        ) { _, bundle ->
            vm.updateItem(UgcDesignDetailFragment.getResult(bundle))
        }

        vm.setupRefreshLoadingCustomLoading(
            ProfileUgcDesignState::entrances,
            binding.loadingUgc,
            binding.refresh,
            loadingAction = {
                // 页面数据本身为空的情况下, 也能下拉刷新, 这个时候应该只显示顶部的loading就可以了
                if (it == null) {
                    binding.loadingUgc.showLoading()
                }
            },
            loadingViewEmptyAction = {
                if (vm.oldState.tagCode == UgcAssetEntrance.TYPE_DESIGN) {
                    if (vm.showGuideBtn) {
                        binding.loadingUgc.showEmpty(
                            if (args.isMe) {
                                getString(R.string.profile_assets_tab_empty)
                            } else {
                                getString(R.string.profile_assets_tab_empty_other)
                            },
                            R.drawable.icon_no_recent_activity,
                            true,
                            getString(R.string.ugc_module_guide_create_one_minute)
                        )
                    } else {
                        binding.loadingUgc.showEmpty(
                            if (args.isMe) {
                                getString(R.string.profile_assets_tab_empty)
                            } else {
                                getString(R.string.profile_assets_tab_empty_other)
                            },
                            R.drawable.icon_no_recent_activity,
                        )
                    }
                } else {
                    binding.loadingUgc.showEmpty()
                }
            }
        ) {
            vm.load()
        }
        vm.onAsync(ProfileUgcDesignState::template, deliveryMode = uniqueOnly(), onFail = { _, _ ->
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = null
        }, onLoading = {
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
        }) {
            editorGameLaunchHelper?.startTemplateGame(
                this,
                it,
                ResIdBean().setCategoryID(CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB)
                    .setIsModuleGuide(true)
            )
        }
        vm.registerAsyncErrorToast(ProfileUgcDesignState::entrances)
    }

    override fun showLoadingUI() {
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
        super.showLoadingUI()
    }

    override fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        super.hideLoadingUI(launchSuccess, msg, needGoMine)
    }

    override fun enableGoMine() = false

    private fun buildTabController() = simpleController(
        vm,
        ProfileUgcDesignState::tags,
        ProfileUgcDesignState::tagCode
    ) { tags, tagCode ->
        tags.forEachIndexed { idx, item ->
            profileUgcAssetTab(item, tagCode, idx, itemListenerV2)
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        ProfileUgcDesignState::tagCode,
        ProfileUgcDesignState::entrances
    ) { tagCode, entrances ->
        entrances()?.forEachIndexed { index, item ->
            profileUgcAssetEntrance(item, tagCode, args.isMe, index, spanSize, itemListenerV2)
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_PROFILE_UGC_DESIGN_TAB

    override fun onResume() {
        super.onResume()
        // 其它好几个地方的操作都可能影响到当前页面的数据, 例如资源详情页, 资源管理页, 创建模组页, 资源点击获取(首页, 游戏详情页)
        // 如果每个地方都去单独标记刷新就太麻烦了, 而且不好维护
        // 所以简单做法就是在onResume的时候重新刷新数据
        vm.load()
    }
}
package com.socialplay.gpark.ui.core.views

import android.view.View
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.view.isVisible
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemUgcCommentExpandBinding
import com.socialplay.gpark.databinding.ViewControlEndLoadMoreBinding
import com.socialplay.gpark.databinding.ViewControlEndLoadMoreVerticalBinding
import com.socialplay.gpark.databinding.ViewVerticalLoadMoreNoTextBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setWidth

data class LoadMoreState(val isEnd: Boolean = false, val needRefresh: Boolean = false)

fun MetaModelCollector.loadMoreFooter(
    loadMore: Async<LoadMoreState> = Loading(),
    id: Long? = null,
    idStr: String? = null,
    spanSize: Int = 1,
    showEnd: Boolean = true,
    endText: String? = null,
    newEndText: String? = null,
    width: Float? = null,
    @ColorRes
    endTextColorRes: Int = 0,
    staggerFullSpan: Boolean = false,
    onLoadMore: () -> Unit,
) {
    add {
        LoadMoreFooter(
            loadMore,
            showEnd,
            endText,
            newEndText,
            width,
            endTextColorRes,
            staggerFullSpan,
            spanSize,
            onLoadMore
        ).apply {
            id?.let { id(id) } ?: idStr?.let { id(idStr) } ?: id("LoadMoreFooter-$it")
            spanSizeOverride { totalSpanCount, _, _ ->
                spanSize.coerceAtMost(totalSpanCount)
            }
        }
    }
}

data class LoadMoreFooter(
    val loadMore: Async<LoadMoreState>,
    val showEnd: Boolean,
    val endText: String?,
    val newEndText: String? = null, // 从2.10.0版本开始支持，以前所有的使用endText的地方默认都不支持了，全局统一，有特殊处理的使用newEndText
    val width: Float?,
    val endTextColorRes: Int,
    val staggerFullSpan: Boolean,
    val spanSize: Int,
    val onLoadMore: () -> Unit,
) : ViewBindingItemModel<ViewControlEndLoadMoreBinding>(
    R.layout.view_control_end_load_more,
    ViewControlEndLoadMoreBinding::bind
) {

    private fun Async<LoadMoreState>.isLoadingVisible() = this is Loading || this is Uninitialized
    private fun Async<LoadMoreState>.isFailedVisible() = this is Fail
    private fun Async<LoadMoreState>.isCompletedVisible() = this is Success && !this.invoke().isEnd
    private fun Async<LoadMoreState>.isEndVisible() = this is Success && this.invoke().isEnd

    override fun onViewAttachedToWindow(view: View) {
        super.onViewAttachedToWindow(view)
        if (view.layoutParams is StaggeredGridLayoutManager.LayoutParams) {
            view.layoutParams = (view.layoutParams as? StaggeredGridLayoutManager.LayoutParams)?.apply {
                isFullSpan = staggerFullSpan
            }
        }
    }

    override fun ViewControlEndLoadMoreBinding.onBind() {
        if (root.layoutParams is StaggeredGridLayoutManager.LayoutParams) {
            root.layoutParams = (root.layoutParams as? StaggeredGridLayoutManager.LayoutParams)?.apply {
                isFullSpan = staggerFullSpan
            }
        }
        if (width != null) {
            root.setWidth(width.toInt())
        }
        loadMoreLoadingView.isVisible = loadMore.isLoadingVisible()
        loadMoreLoadFailView.isVisible = loadMore.isFailedVisible()
        loadMoreLoadCompleteView.isVisible = loadMore.isCompletedVisible()
        loadMoreLoadEndView.isVisible = if (showEnd && loadMore.isEndVisible()) {
            tvMoreLoadEnd.text = newEndText ?: root.context.getString(R.string.footer_load_end)
            if (endTextColorRes != 0) {
                tvMoreLoadEnd.setTextColorByRes(endTextColorRes)
            } else {
                tvMoreLoadEnd.setTextColorByRes(R.color.textColorSecondary)
            }
            true
        } else {
            false
        }
        if (loadMore is Uninitialized || loadMore.isCompletedVisible()) {
            onLoadMore()
        }
        loadMoreLoadFailView.setOnAntiViolenceClickListener {
            onLoadMore()
        }
        loadMoreLoadCompleteView.setOnAntiViolenceClickListener {
            onLoadMore()
        }
    }

    override fun ViewControlEndLoadMoreBinding.onUnbind() {
        loadMoreLoadFailView.setOnClickListener(null)
    }
}

fun MetaModelCollector.loadMoreVerticalFooter(
    loadMore: Async<LoadMoreState> = Loading(),
    id: Long? = null,
    idStr: String? = null,
    spanSize: Int = 1,
    showRetry: Boolean = true,
    showEnd: Boolean = true,
    onLoadMore: () -> Unit
) {
    add {
        LoadMoreVerticalFooter(loadMore, showRetry, showEnd, spanSize, onLoadMore).apply {
            id?.let { id(id) } ?: idStr?.let { id(idStr) } ?: id("LoadMoreVerticalFooter-$it")
            spanSizeOverride { totalSpanCount, _, _ ->
                spanSize.coerceAtMost(totalSpanCount)
            }
        }
    }
}

data class LoadMoreVerticalFooter(
    val loadMore: Async<LoadMoreState>,
    val showRetry: Boolean,
    val showEnd: Boolean,
    val spanSize: Int,
    val onLoadMore: () -> Unit
) : ViewBindingItemModel<ViewControlEndLoadMoreVerticalBinding>(
    R.layout.view_control_end_load_more_vertical,
    ViewControlEndLoadMoreVerticalBinding::bind
) {

    private fun Async<LoadMoreState>.isLoadingVisible() = this is Loading || this is Uninitialized
    private fun Async<LoadMoreState>.isFailedVisible() = this is Fail
    private fun Async<LoadMoreState>.isCompletedVisible() = this is Success && !this.invoke().isEnd
    private fun Async<LoadMoreState>.isEndVisible() = this is Success && this.invoke().isEnd

    override fun ViewControlEndLoadMoreVerticalBinding.onBind() {
        loadMoreLoadingView.isVisible = loadMore.isLoadingVisible()
        loadMoreLoadFailView.isVisible = showRetry && loadMore.isFailedVisible()
        loadMoreLoadCompleteView.isVisible = loadMore.isCompletedVisible()
        loadMoreLoadEndView.isVisible = showEnd && loadMore.isEndVisible()
        if (loadMore is Uninitialized || loadMore.isCompletedVisible()) {
            onLoadMore()
        }
        loadMoreLoadFailView.setOnAntiViolenceClickListener {
            onLoadMore()
        }
    }

    override fun ViewControlEndLoadMoreVerticalBinding.onUnbind() {
        loadMoreLoadFailView.setOnClickListener(null)
    }
}
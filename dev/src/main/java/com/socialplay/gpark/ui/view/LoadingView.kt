package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Parcel
import android.os.Parcelable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.Button
import androidx.annotation.AttrRes
import androidx.annotation.RequiresApi
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.withStyledAttributes
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewLoadingBinding
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.getStringByResIfNull
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import org.koin.core.context.GlobalContext

enum class LoadingState {
    LOADING,
    NO_MSG,
    ERROR,
    EMPTY,
    NET_ERROR
}

/**
 * create by: shuai on 2025/7/14
 */
class LoadingView : ConstraintLayout {
    lateinit var bind: ViewLoadingBinding

    /**
     * 0 加载中
     * 1 无消息（IM使用）
     * 2 通用Error
     * 3 无内容
     * 4 网络问题
     */
    private var loadingState: Int = LoadingState.LOADING.ordinal
    private var tipsColor: Int? = null
    private var btnText: String? = null
    private var btnTextColor: Int? = null
    private var btnBackground: Drawable? = null
    private var tipsText: String? = null
    private var loadingText: String? = null
    private var showBtn: Boolean = false
    private var verticalBias: Float? = null

    constructor(context: Context) : super(context) {
        inflateLayout(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        inflateLayout(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        inflateLayout(context, attrs)
    }

    private fun inflateLayout(context: Context, attrs: AttributeSet?) {
        bind = ViewLoadingBinding.inflate(LayoutInflater.from(context), this)
        if (attrs != null) {
            context.withStyledAttributes(attrs, R.styleable.LoadingView) {
                loadingState = getInt(R.styleable.LoadingView_loadingState, loadingState)
                showBtn = getBoolean(R.styleable.LoadingView_showBtn, showBtn)
                loadingText = getStringByResIfNull(getString(R.styleable.LoadingView_loadingText), R.string.loading)
                tipsText = getStringByResIfNull(getString(R.styleable.LoadingView_tipsText), R.string.no_data)
                tipsColor = getColor(R.styleable.LoadingView_tipsColor, getColorByRes(R.color.Gray_900))
                btnText = getStringByResIfNull(getString(R.styleable.LoadingView_btnText), R.string.retry_cap)
                btnTextColor = getColor(R.styleable.LoadingView_btnTextColor, getColorByRes(R.color.Gray_1000))
                btnBackground = getDrawable(R.styleable.LoadingView_btnBackground) ?: getDrawableByRes(R.drawable.selector_button)
            }
        }
        initView()
    }

    private fun initView() {
        refreshState()
    }

    private fun refreshState(needRefresh: Boolean = false) {
        post {
            bind.tvLoading.text = loadingText
            bind.tvTips.text = tipsText
            bind.tvTips.setTextColor(tipsColor ?: getColorByRes(R.color.Gray_900))
            bind.btnLoading.text = btnText
            bind.btnEmpty.text = btnText
            bind.btnLoading.setTextColor(btnTextColor ?: getColorByRes(R.color.Gray_1000))
            bind.btnEmpty.setTextColor(btnTextColor ?: getColorByRes(R.color.Gray_1000))
            bind.btnLoading.background = btnBackground
            bind.btnEmpty.background = btnBackground
            if (showBtn && loadingState == LoadingState.EMPTY.ordinal) {
                // 空数据的时候，不需要刷新，给的是其他回调
                bind.btnEmpty.visible()
                bind.btnLoading.gone()
            } else if (showBtn) {
                bind.btnEmpty.gone()
                bind.btnLoading.visible()
            } else {
                bind.btnLoading.invisible()
                bind.btnEmpty.gone()
            }
            bind.llPlaceholder.gone()
            bind.llLoading.gone()
            when (loadingState) {
                LoadingState.LOADING.ordinal -> {
                    // 加载中
                    bind.llLoading.visible()
                    bind.lottieLoading.cancelAnimation()
                    bind.lottieLoading.playAnimation()
                }

                LoadingState.NO_MSG.ordinal -> {
                    // 无消息(IM使用)
                    bind.llPlaceholder.visible()
                    bind.lottiePlaceholder.setAnimation("lottie_no_msg.zip")
                    bind.lottiePlaceholder.cancelAnimation()
                    bind.lottiePlaceholder.playAnimation()
                }

                LoadingState.ERROR.ordinal -> {
                    // 通用Error
                    bind.llPlaceholder.visible()
                    bind.lottiePlaceholder.setAnimation("lottie_error.zip")
                    bind.lottiePlaceholder.cancelAnimation()
                    bind.lottiePlaceholder.playAnimation()
                }

                LoadingState.EMPTY.ordinal -> {
                    // 无内容
                    bind.llPlaceholder.visible()
                    bind.lottiePlaceholder.setAnimation("lottie_empty.zip")
                    bind.lottiePlaceholder.cancelAnimation()
                    bind.lottiePlaceholder.playAnimation()
                }

                LoadingState.NET_ERROR.ordinal -> {
                    // 网络问题
                    bind.llPlaceholder.visible()
                    bind.lottiePlaceholder.setAnimation("lottie_net_error.zip")
                    bind.lottiePlaceholder.cancelAnimation()
                    bind.lottiePlaceholder.playAnimation()
                }
            }
            if (needRefresh) {
                invalidate()
            }
        }
    }

    /**
     * 设置布局的竖直方向偏移量，
     * 注意：外层布局必须为ConstraintLayout
     */
    @Throws(ClassCastException::class)
    fun setVerticalBias(bias: Float) {
        verticalBias = bias
        // TODO 需要查看使用到的地方，是否布局存在显示问题
//        if (verticalBias != 0.5F) {
//            val cs = ConstraintSet()
//            cs.clone(bind.root as ConstraintLayout)
//            cs.setVerticalBias(bind.llPlaceholder.id, bias)
//            cs.setVerticalBias(bind.tvLoading.id, bias)
////            cs.setVerticalBias(bind.llOtherError.id, bias)
//            cs.setVerticalBias(bind.tvTips.id, bias)
//            cs.applyTo(bind.root as ConstraintLayout)
//        }
    }

    fun setShowBtn(showBtn: Boolean) {
        this.showBtn = showBtn
        refreshState()
    }


    fun setEmptyBtnClick(onClick: () -> Unit): LoadingView {
        bind.btnEmpty.setOnAntiViolenceClickListener {
            onClick()
        }
        return this
    }

    fun setRetry(retry: () -> Unit): LoadingView {
        bind.btnLoading.setOnAntiViolenceClickListener {
            if (!NetUtil.isNetworkAvailable()) {
                ToastUtil.showShort(it.context, R.string.abnormal_network)
            } else {
                showLoading(true)
                retry.invoke()
            }
        }
        return this
    }

    fun hide() {
        if (!bind.root.isGone) {
            bind.root.isGone = true
            bind.lottieLoading.cancelAnimation()
            bind.lottiePlaceholder.cancelAnimation()
        }
    }

    fun showError(forceShowNetworkError: Boolean = false) {
        if (!forceShowNetworkError && NetUtil.isNetworkAvailable()) {
            if (!bind.root.isVisible) {
                bind.root.isVisible = true
            }
            bind.root.isClickable = true
            loadingState = LoadingState.ERROR.ordinal
            tipsText = context.getString(R.string.common_error)
            showBtn = true
            refreshState()
        } else {
            showNetError()
        }
    }

    fun showLoading(showTips: Boolean = true, msg: String? = null, backgroundColor: Int) {
        showLoading(showTips, msg)
        background = backgroundColor.toDrawable()
    }

    fun showLoading(showTips: Boolean = true, loadingTips: String? = null) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = true
        // 这里后续看具体需求，有需要隐藏的再隐藏。感觉都不是啥强需求
//        bind.tvLoading.visible(showTips)
        loadingText = if (!loadingTips.isNullOrEmpty()) {
            loadingTips
        } else {
            context.getString(R.string.loading)
        }
        loadingState = LoadingState.LOADING.ordinal
        refreshState()
    }

    fun showNoMsg(tips: String? = null) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = true
        tipsText = if (!tips.isNullOrEmpty()) {
            tips
        } else {
            context.getString(R.string.no_messages)
        }
        loadingState = LoadingState.NO_MSG.ordinal
        refreshState()
    }

    fun with(loading: Boolean) {
        if (loading) {
            showLoading(true)
        } else {
            hide()
        }
    }

    fun with(state: Async<*>) {
        when (state) {
            is Loading -> showLoading(true)
            is Success -> hide()
            is Fail -> showError()
            else -> hide()
        }
    }

    /**
     * 从2.10.0版本开始，全局统一，不支持外部自定义资源
     */
    fun showEmpty(msg: String = GlobalContext.get().get<Context>().getString(R.string.no_data), resId: Int = 0, showBtn: Boolean = false, btnTxt: String? = null, enableClick: Boolean = false) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = enableClick
        this.showBtn = showBtn
        this.btnText = btnTxt
        tipsText = msg
        loadingState = LoadingState.EMPTY.ordinal
        refreshState()
    }

    /**
     * 从2.10.0版本开始，全局统一，不支持外部自定义资源
     */
    fun showOtherEmpty(msg: String = GlobalContext.get().get<Context>().getString(R.string.no_data), resId: Int = 0) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = true
        tipsText = msg
        loadingState = LoadingState.EMPTY.ordinal
        refreshState()
    }

    /**
     * 网络链接失败
     */
    private fun showNetError() {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = true
        loadingState = LoadingState.NET_ERROR.ordinal
        tipsText = context.getString(R.string.net_unavailable)
        showBtn = true
        if (btnText == null) {
            btnText = context.getString(R.string.retry_cap)
        }
        refreshState()
    }

    fun btnEmpty(): Button {
        return bind.btnLoading
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        // 6.0以下调用super会崩溃, 但是不调用也会崩溃, 所以需要针对性catch
        runCatching { super.onRestoreInstanceState(state) }
        state ?: return
        visibility = (state as SavedState).visibility
    }

    override fun onSaveInstanceState(): Parcelable {
        val superState = super.onSaveInstanceState()
        val ss = SavedState(superState)
        ss.visibility = visibility
        return ss
    }

    internal class SavedState : BaseSavedState {
        var visibility: Int = 0

        @RequiresApi(Build.VERSION_CODES.N)
        constructor(source: Parcel?, loader: ClassLoader?) : super(source, loader) {
            visibility = source?.readInt() ?: 0
        }

        constructor(source: Parcel?) : super(source) {
            visibility = source?.readInt() ?: 0
        }

        constructor(superState: Parcelable?) : super(superState)

        override fun writeToParcel(out: Parcel, flags: Int) {
            super.writeToParcel(out, flags)
            out.writeInt(visibility)
        }

        companion object CREATOR : Parcelable.ClassLoaderCreator<SavedState> {

            override fun createFromParcel(source: Parcel?, loader: ClassLoader?): SavedState {
                return if (Build.VERSION.SDK_INT >= 24) SavedState(source, loader) else SavedState(
                    source
                )
            }

            override fun createFromParcel(source: Parcel?): SavedState {
                return SavedState(source)
            }

            override fun newArray(size: Int): Array<SavedState?> {
                return arrayOfNulls(size)
            }
        }

    }

}
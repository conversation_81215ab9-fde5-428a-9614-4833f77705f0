package com.socialplay.gpark.ui.post.feed.tag

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.mw.develop.ui.core.epoxy.map
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCard
import com.socialplay.gpark.data.model.community.RecommendFeedCardListResponse
import com.socialplay.gpark.data.model.community.UserFeedUser
import com.socialplay.gpark.data.model.creator.CreatorFrameResult
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.default
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModelV2
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelStateV2
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class FollowingCommunityFeedModelState(
    override val refresh: Async<List<RecommendFeedCard>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    override val uniqueTag: Int = 0,
    val followInfo: Async<CreatorFrameResult?> = Uninitialized,
    val recommendUsers: Async<List<UserFeedUser>> = Uninitialized,
    val recommendUsersUniqueTag: Int = 0,
    val offset: String? = null,
    val scrollToTop: Async<Boolean> = Uninitialized,
) : ICommunityFeedModelStateV2 {

    override fun updateFeedData(list: List<RecommendFeedCard>): ICommunityFeedModelStateV2 {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelStateV2 {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelStateV2 {
        return copy(notifyCheckVideo = checkVideo)
    }

    /**
     * 帖子按照 postId 去重
     */
    private fun List<RecommendFeedCard>.distinctPostFeedCard(): List<RecommendFeedCard>? {
        val postIds = mutableSetOf<String>()
        return this.filter { item ->
            if (item is PostFeedCard) {
                if (item.postId.isNullOrEmpty()) {
                    false
                } else {
                    postIds.add(item.postId)
                }
            } else {
                true
            }
        }
    }

    override fun feedRefresh(result: Async<RecommendFeedCardListResponse>): ICommunityFeedModelStateV2 {
        val newRefresh = result.map { response ->
            response.list?.distinctPostFeedCard() ?: emptyList()
        }
        return copy(
            refresh = newRefresh.default(refresh.invoke()),
            offset = result()?.offset,
            uniqueTag = uniqueTag + 1,
            loadMore = result.map { LoadMoreState(isEnd = result()?.hasMore == false) }
        )
    }

    override fun feedLoadMore(result: Async<RecommendFeedCardListResponse>): ICommunityFeedModelStateV2 {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke() ?: emptyList()
                result.map { response ->
                    if (oldList.isNullOrEmpty()) {
                        response.list ?: emptyList()
                    } else {
                        oldList + (response.list ?: emptyList())
                    }.distinctPostFeedCard() ?: emptyList()
                }
            } else {
                refresh
            },
            offset = if (result is Success) {
                result().offset
            } else {
                offset
            },
            loadMore = result.map { LoadMoreState(result()?.hasMore == false) }
        )
    }
}

class FollowingCommunityFeedViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: FollowingCommunityFeedModelState
) : BaseCommunityFeedViewModelV2<FollowingCommunityFeedModelState>(
    repository,
    accountInteractor,
    initialState
) {

    private var shouldRefresh = false

    fun checkRefreshPage() {
        if (shouldRefresh) {
            refreshPage()
        }
    }

    /**
     * 刷新页面一共会请求两个接口, loadFollowCreatorAndWorks() 和 refreshRecommendedFeed()
     * 并行请求的话, 状态刷新不好把控, 用串行请求
     */
    fun refreshPage() = withState { oldState ->
        shouldRefresh = false
        setState {
            copy(
                followInfo = Loading(oldState.followInfo.invoke()),
                recommendUsers = Loading(oldState.recommendUsers.invoke()),
                refresh = Loading(oldState.refresh.invoke()),
            )
        }
        loadFollowCreatorAndWorks()
    }

    private fun loadFollowCreatorAndWorks() {
        viewModelScope.launch {
            val frameResult = runCatching {
                repository.getKolFrameList().singleOrNull()
            }.getOrElse { null }
            val frameData = frameResult?.data
            val followUserList = frameData?.followUserList ?: emptyList()
            val followGames = frameData?.followGames ?: emptyList()
            setState {
                copy(
                    followInfo = Success(frameData),
                )
            }
            if (followUserList.isEmpty()) {
                // 当前用户没有关注过任何用户, 则加载推荐用户信息
                loadRecommendUsers()
            } else {
                refreshRecommendedFeed()
            }
        }
    }

    fun loadRecommendUsers() {
        repository.getCommunityRecommendUsers(
            pageSize = LOAD_RECOMMEND_USERS_PAGE_SIZE,
            offset = null,
        ).map {
            it.list ?: emptyList()
        }.execute {
            copy(
                recommendUsers = it.default(oldState.recommendUsers.invoke()),
                recommendUsersUniqueTag = oldState.recommendUsersUniqueTag + 1
            )
        }
    }

    fun refreshRecommendedFeed() {
        withState { s ->
            if (oldState.loadMore is Loading) return@withState
            // 处理type的string和int转换
            repository.getCommunityRecommendFollows(
                pageSize = PAGE_SIZE,
            ).map {
                notifyCheckVideo()
                it.filterInvalidElements(accountInteractor.curUuid)
            }.execute { resultAsync ->
                feedRefresh(resultAsync) as FollowingCommunityFeedModelState
            }
        }
    }

    fun loadMoreRecommendedFeed() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            repository.getCommunityRecommendFollows(
                pageSize = PAGE_SIZE,
                offset = oldState.offset,
            ).map {
                it.filterInvalidElements(accountInteractor.curUuid)
            }.execute { result ->
                feedLoadMore(result) as FollowingCommunityFeedModelState
            }
        }
    }

    override fun receivedFollowEvent(event: UserFollowEvent) {
        // 点击社区 follow tab 中的关注按钮后, 刷新当前页面表现有点奇怪
        // 所以只有其它页面的点击关注, 才刷新 follow tab 中的数据
        if (event.from == UserFollowEvent.FROM_FOLLOW_TAB) {
            val oldList = oldState.recommendUsers.invoke() ?: return
            val newList = oldList.toMutableList()
            var needUpdate = false
            oldList.forEachIndexed { index, item ->
                if (item.uid == event.uuid) {
                    needUpdate = true
                    newList[index] = oldList[index].copy(
                        followStatus = event.followStatus
                    )
                }
            }
            if (needUpdate) {
                setState {
                    copy(
                        recommendUsers = recommendUsers.copyEx(newList)
                    )
                }
            }
        } else {
            shouldRefresh = true
        }
    }

    companion object :
        KoinViewModelFactory<FollowingCommunityFeedViewModel, FollowingCommunityFeedModelState>() {

        private const val LOAD_RECOMMEND_USERS_PAGE_SIZE = 9
        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: FollowingCommunityFeedModelState
        ): FollowingCommunityFeedViewModel {
            return FollowingCommunityFeedViewModel(get(), get(), state)
        }
    }
}
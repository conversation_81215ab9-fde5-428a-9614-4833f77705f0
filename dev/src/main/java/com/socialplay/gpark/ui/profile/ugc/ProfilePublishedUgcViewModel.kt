package com.socialplay.gpark.ui.profile.ugc

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.editor.MyCreationsV4Request
import com.socialplay.gpark.data.model.editor.UgcGameInfo.Games
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.default
import com.socialplay.gpark.ui.core.maverick.ifLoading
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.extension.getWithIndex
import com.socialplay.gpark.util.extension.moveNewTo
import com.socialplay.gpark.util.extension.replaceAt
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc: 已发布ugc
 */
data class ProfilePublishedUgcModelState(
    // 已发布分页orderId
    val uuid: String?,
    val asyncList: Async<List<Games>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val deleteUgcResult: Async<String> = Uninitialized,
    val pinResult: Async<String> = Uninitialized,
    val gameType: Int = GAME_TYPE_ALL,
    val tabPosition: Int = 0
) : MavericksState {

    companion object {
        const val GAME_TYPE_ALL = 3
        const val GAME_TYPE_PGC = 2
        const val GAME_TYPE_UGC = 1
    }

    constructor(args: ProfilePublishedUgcFragmentArgs) : this(args.otherUid)

    val list: List<Games>
        get() = asyncList() ?: emptyList()
}

class ProfilePublishedUgcViewModel(
    private val repository: IMetaRepository,
    val accountInteractor: AccountInteractor,
    initialState: ProfilePublishedUgcModelState
) : BaseViewModel<ProfilePublishedUgcModelState>(initialState) {

    companion object :
        KoinViewModelFactory<ProfilePublishedUgcViewModel, ProfilePublishedUgcModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: ProfilePublishedUgcModelState
        ): ProfilePublishedUgcViewModel {
            return ProfilePublishedUgcViewModel(get(), get(), state)
        }
    }

    private var sinceId: String? = null
    private var pgcSinceId: String? = null
    private var ugcSinceId: String? = null

    init {
        refresh()
    }

    fun updateTabSelection(position: Int, gameType: Int, force: Boolean = false) = withState { s ->
        if (!force && s.loadMore is Loading) return@withState
        if (force || s.tabPosition != position || s.gameType != gameType) {
            setState {
                copy(tabPosition = position, gameType = gameType)
            }
            refresh(gameType)
        }
    }

    fun refresh(gameType: Int? = null, force: Boolean = false) = withState { s ->
        if ((!force && s.loadMore is Loading )|| s.uuid.isNullOrEmpty()) return@withState
        repository.getMyCreations(
            MyCreationsV4Request(
                orderId = null,
                userUuid = s.uuid,
                gameType = gameType ?: s.gameType,
                orderType = MyCreationsV4Request.ORDER_TYPE_TIME
            )
        ).map {
            val refreshWorks = repository.getRefreshSelfWorks(true)
            updateGameStatus(it.games, refreshWorks)
            it
        }.execute { result ->
            val end = result()?.end == true || result()?.games.isNullOrEmpty() || sinceId == null
            copy(
                asyncList = result.map { it.games?.distinctBy { it.id }.orEmpty() }.default(asyncList.invoke()),
                loadMore = result.map { LoadMoreState(end) },
            )
        }
    }

    fun loadMore() = withState { s ->
        if (s.asyncList is Loading || s.uuid.isNullOrEmpty()) return@withState
        repository.getMyCreations(
            MyCreationsV4Request(
                orderId = sinceId,
                userUuid = s.uuid,
                gameType = s.gameType,
                orderType = MyCreationsV4Request.ORDER_TYPE_TIME
            )
        ).map {
            updateGameStatus(it.games, null)
            it
        }.execute { result ->
            val end = result()?.end == true || result()?.games.isNullOrEmpty() || sinceId == null
            copy(
                asyncList = if (result is Success) {
                    val oldList = list
                    result.map { wrapper ->
                        if (oldList.isEmpty()) {
                            wrapper.games.orEmpty()
                        } else {
                            oldList + wrapper.games.orEmpty()
                        }.distinctBy { it.id }
                    }
                } else {
                    asyncList
                },
                loadMore = result.map { LoadMoreState(end) },
            )
        }
    }

    fun updateDeleteResult(ugId: String) = withState { s ->
        val oldList = oldState.asyncList()?.toMutableList()
        val del = oldList?.removeAll { it.id == ugId } == true
        val newList = if (del) {
            oldList.toList()
        } else {
            null
        }
        setState {
            copy(
                asyncList = if (newList != null) {
                    asyncList.map { newList }
                } else {
                    asyncList
                },
            )
        }
    }

    fun deletePublishedUgc(ugId: String) = withState { s ->
        if (s.asyncList is Loading) return@withState
        repository.deleteEditorPublishedV2(ugId).map {
            val oldList = oldState.asyncList()
            val newList = oldList?.toMutableList()
            val del = newList?.removeAll { it.id == ugId } == true
            if (del) {
                newList?.toList()
            } else {
                null
            }
        }.execute {
            when (it) {
                is Success -> {
                    val newList = it()
                    copy(
                        asyncList = if (newList != null) {
                            asyncList.map { newList }
                        } else {
                            asyncList
                        },
                        deleteUgcResult = Success(ugId)
                    )
                }

                else -> {
                    copy(deleteUgcResult = it.map { ugId })
                }
            }
        }
    }

    fun pin(game: Games, position: Int) = withState { s ->
        if (s.asyncList is Loading) return@withState
        repository.pinMyCreationsV2(
            if (game.isPgc) game.gameCode else game.id,
            !game.topOn
        ).map {
            val oldList = oldState.asyncList() ?: return@map null
            val (oldIdx, oldItem) = oldList.getWithIndex(position) {
                it.id == game.id && it.gameType == game.gameType
            } ?: return@map null
            if (game.topOn != oldItem.topOn) {
                // 置顶状态不一样，不用处理
                return@map null
            } else if (!game.topOn) {
                // 需要置顶，放到第一个
                // 如果有之前置顶过的同类型游戏，先取消置顶
                val oldTopIdx = oldList.indexOfLast { it.topOn && it.gameType == game.gameType }
                val temp = if (oldTopIdx != -1) {
                    val lastTopIdx = oldList.indexOfLast { it.topOn }
                    // 如果不是最后一个置顶，就放到最后一个置顶的后面
                    if (oldTopIdx != lastTopIdx) {
                        oldList.moveNewTo(
                            oldTopIdx,
                            lastTopIdx,
                            oldList[oldTopIdx],
                            oldList[oldTopIdx].copy(topOn = false)
                        )
                    } else {
                        oldList.replaceAt(oldTopIdx, oldList[oldTopIdx].copy(topOn = false))
                    }
                } else {
                    oldList
                }
                val tempFirst = temp?.getOrNull(0)
                if (tempFirst?.topOn == true) {
                    if (oldItem.isUgc && tempFirst.isPgc) {
                        temp.moveNewTo(oldIdx, 1, oldItem, oldItem.copy(topOn = true))
                    } else {
                        temp.moveNewTo(oldIdx, 0, oldItem, oldItem.copy(topOn = true))
                    }
                } else {
                    temp.moveNewTo(oldIdx, 0, oldItem, oldItem.copy(topOn = true))
                }
            } else {
                // 需要下掉置顶
                val lastTopIdx = oldList.indexOfLast { it.topOn }
                if (lastTopIdx == -1) {
                    return@map null
                } else if (oldIdx == lastTopIdx) {
                    // 本来就是最后一个置顶，只需要更改状态
                    oldList.replaceAt(lastTopIdx, oldItem.copy(topOn = false))
                } else {
                    // 放到最后一个置顶的后面
                    oldList.moveNewTo(
                        oldIdx,
                        lastTopIdx,
                        oldItem,
                        oldItem.copy(topOn = false)
                    )
                }
            }
        }.execute {
            when (it) {
                is Success -> {
                    val newList = it()
                    copy(
                        asyncList = if (newList != null) {
                            asyncList.map { newList }
                        } else {
                            asyncList
                        },
                        pinResult = Success(game.id)
                    )
                }

                else -> {
                    copy(pinResult = it.map { game.id })
                }
            }
        }
    }

    fun updateUuid(newUuid: String) = withState { s ->
        if (s.uuid == newUuid) return@withState
        setState { copy(uuid = newUuid) }
        updateTabSelection(0, ProfilePublishedUgcModelState.GAME_TYPE_ALL, force = true)
    }

    private fun updateGameStatus(games: MutableList<Games>?, refreshWorks: List<String>? = null) {
        sinceId = games?.lastOrNull()?.orderId
        pgcSinceId = games?.lastOrNull { it.isPgc }?.orderId
        ugcSinceId = games?.lastOrNull { it.isUgc }?.orderId
        if (!games.isNullOrEmpty() && !refreshWorks.isNullOrEmpty()) {
            val gamesTempMap = games.associateBy { it.id }
            refreshWorks.forEach { gameId ->
                gamesTempMap[gameId]?.highlight = true
            }
        }
    }

    fun onPageResume() {
        viewModelScope.launch {
            val refreshSelfWorks = repository.getRefreshSelfWorks(false)
            if (!refreshSelfWorks.isNullOrEmpty()) {
                refresh()
            }
        }
    }
}